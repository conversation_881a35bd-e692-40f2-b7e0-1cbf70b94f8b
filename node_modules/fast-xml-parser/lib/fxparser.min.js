!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.XMLParser=e():t.XMLParser=e()}(this,(()=>(()=>{"use strict";var t={d:(e,r)=>{for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{default:()=>et});var r={preserveOrder:!1,attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,removeNSPrefix:!1,allowBooleanAttributes:!1,parseTagValue:!0,parseAttributeValue:!1,trimValues:!0,cdataPropName:!1,numberParseOptions:{hex:!0,leadingZeros:!0,eNotation:!0},tagValueProcessor:function(t,e){return e},attributeValueProcessor:function(t,e){return e},stopNodes:[],alwaysCreateTextNode:!1,isArray:function(){return!1},commentPropName:!1,unpairedTags:[],processEntities:!0,htmlEntities:!1,ignoreDeclaration:!1,ignorePiTags:!1,transformTagName:!1,transformAttributeName:!1,updateTag:function(t,e,r){return t},captureMetaData:!1},n=":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",i=new RegExp("^["+n+"]["+n+"\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$");function a(t,e){for(var r=[],n=e.exec(t);n;){var i=[];i.startIndex=e.lastIndex-n[0].length;for(var a=n.length,o=0;o<a;o++)i.push(n[o]);r.push(i),n=e.exec(t)}return r}var o,s=function(t){return!(null==i.exec(t))};o="function"!=typeof Symbol?"@@xmlMetadata":Symbol("XML Node Metadata");var l=function(){function t(t){this.tagname=t,this.child=[],this[":@"]={}}var e=t.prototype;return e.add=function(t,e){var r;"__proto__"===t&&(t="#__proto__"),this.child.push(((r={})[t]=e,r))},e.addChild=function(t,e){var r,n;"__proto__"===t.tagname&&(t.tagname="#__proto__"),t[":@"]&&Object.keys(t[":@"]).length>0?this.child.push(((r={})[t.tagname]=t.child,r[":@"]=t[":@"],r)):this.child.push(((n={})[t.tagname]=t.child,n)),void 0!==e&&(this.child[this.child.length-1][o]={startIndex:e})},t.getMetaDataSymbol=function(){return o},t}();function u(t,e){var r={};if("O"!==t[e+3]||"C"!==t[e+4]||"T"!==t[e+5]||"Y"!==t[e+6]||"P"!==t[e+7]||"E"!==t[e+8])throw new Error("Invalid Tag instead of DOCTYPE");e+=9;for(var n=1,i=!1,a=!1;e<t.length;e++)if("<"!==t[e]||a)if(">"===t[e]){if(a?"-"===t[e-1]&&"-"===t[e-2]&&(a=!1,n--):n--,0===n)break}else"["===t[e]?i=!0:t[e];else{if(i&&c(t,"!ENTITY",e)){var o,s=void 0,l=d(t,(e+=7)+1);o=l[0],s=l[1],e=l[2],-1===s.indexOf("&")&&(r[o]={regx:RegExp("&"+o+";","g"),val:s})}else if(i&&c(t,"!ELEMENT",e))e=p(t,(e+=8)+1).index;else if(i&&c(t,"!ATTLIST",e))e+=8;else if(i&&c(t,"!NOTATION",e))e=g(t,(e+=9)+1).index;else{if(!c(t,"!--",e))throw new Error("Invalid DOCTYPE");a=!0}n++}if(0!==n)throw new Error("Unclosed DOCTYPE");return{entities:r,i:e}}var f=function(t,e){for(;e<t.length&&/\s/.test(t[e]);)e++;return e};function d(t,e){e=f(t,e);for(var r="";e<t.length&&!/\s/.test(t[e])&&'"'!==t[e]&&"'"!==t[e];)r+=t[e],e++;if(v(r),e=f(t,e),"SYSTEM"===t.substring(e,e+6).toUpperCase())throw new Error("External entities are not supported");if("%"===t[e])throw new Error("Parameter entities are not supported");var n=h(t,e,"entity");return e=n[0],[r,n[1],--e]}function g(t,e){e=f(t,e);for(var r="";e<t.length&&!/\s/.test(t[e]);)r+=t[e],e++;v(r),e=f(t,e);var n=t.substring(e,e+6).toUpperCase();if("SYSTEM"!==n&&"PUBLIC"!==n)throw new Error('Expected SYSTEM or PUBLIC, found "'+n+'"');e+=n.length,e=f(t,e);var i=null,a=null;if("PUBLIC"===n){var o=h(t,e,"publicIdentifier");if(e=o[0],i=o[1],'"'===t[e=f(t,e)]||"'"===t[e]){var s=h(t,e,"systemIdentifier");e=s[0],a=s[1]}}else if("SYSTEM"===n){var l=h(t,e,"systemIdentifier");if(e=l[0],!(a=l[1]))throw new Error("Missing mandatory system identifier for SYSTEM notation")}return{notationName:r,publicIdentifier:i,systemIdentifier:a,index:--e}}function h(t,e,r){var n="",i=t[e];if('"'!==i&&"'"!==i)throw new Error('Expected quoted string, found "'+i+'"');for(e++;e<t.length&&t[e]!==i;)n+=t[e],e++;if(t[e]!==i)throw new Error("Unterminated "+r+" value");return[++e,n]}function p(t,e){e=f(t,e);for(var r="";e<t.length&&!/\s/.test(t[e]);)r+=t[e],e++;if(!v(r))throw new Error('Invalid element name: "'+r+'"');var n="";if("E"===t[e=f(t,e)]&&c(t,"MPTY",e))e+=6;else if("A"===t[e]&&c(t,"NY",e))e+=4;else{if("("!==t[e])throw new Error('Invalid Element Expression, found "'+t[e]+'"');for(e++;e<t.length&&")"!==t[e];)n+=t[e],e++;if(")"!==t[e])throw new Error("Unterminated content model")}return{elementName:r,contentModel:n.trim(),index:e}}function c(t,e,r){for(var n=0;n<e.length;n++)if(e[n]!==t[r+n+1])return!1;return!0}function v(t){if(s(t))return t;throw new Error("Invalid entity name "+t)}const m=/^[-+]?0x[a-fA-F0-9]+$/,x=/^([\-\+])?(0*)([0-9]*(\.[0-9]*)?)$/,b={hex:!0,leadingZeros:!0,decimalPoint:".",eNotation:!0};const N=/^([-+])?(0*)(\d*(\.\d*)?[eE][-\+]?\d+)$/;function E(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var y=function(t){var e;this.options=t,this.currentNode=null,this.tagsNodeStack=[],this.docTypeEntities={},this.lastEntities={apos:{regex:/&(apos|#39|#x27);/g,val:"'"},gt:{regex:/&(gt|#62|#x3E);/g,val:">"},lt:{regex:/&(lt|#60|#x3C);/g,val:"<"},quot:{regex:/&(quot|#34|#x22);/g,val:'"'}},this.ampEntity={regex:/&(amp|#38|#x26);/g,val:"&"},this.htmlEntities={space:{regex:/&(nbsp|#160);/g,val:" "},cent:{regex:/&(cent|#162);/g,val:"¢"},pound:{regex:/&(pound|#163);/g,val:"£"},yen:{regex:/&(yen|#165);/g,val:"¥"},euro:{regex:/&(euro|#8364);/g,val:"€"},copyright:{regex:/&(copy|#169);/g,val:"©"},reg:{regex:/&(reg|#174);/g,val:"®"},inr:{regex:/&(inr|#8377);/g,val:"₹"},num_dec:{regex:/&#([0-9]{1,7});/g,val:function(t,e){return String.fromCodePoint(Number.parseInt(e,10))}},num_hex:{regex:/&#x([0-9a-fA-F]{1,6});/g,val:function(t,e){return String.fromCodePoint(Number.parseInt(e,16))}}},this.addExternalEntities=T,this.parseXml=O,this.parseTextData=w,this.resolveNameSpace=I,this.buildAttributesMap=A,this.isItStopNode=M,this.replaceEntitiesValue=C,this.readStopNodeData=k,this.saveTextToParentTag=D,this.addChild=P,this.ignoreAttributesFn="function"==typeof(e=this.options.ignoreAttributes)?e:Array.isArray(e)?function(t){for(var r,n=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return E(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?E(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0;return function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(e);!(r=n()).done;){var i=r.value;if("string"==typeof i&&t===i)return!0;if(i instanceof RegExp&&i.test(t))return!0}}:function(){return!1}};function T(t){for(var e=Object.keys(t),r=0;r<e.length;r++){var n=e[r];this.lastEntities[n]={regex:new RegExp("&"+n+";","g"),val:t[n]}}}function w(t,e,r,n,i,a,o){if(void 0!==t&&(this.options.trimValues&&!n&&(t=t.trim()),t.length>0)){o||(t=this.replaceEntitiesValue(t));var s=this.options.tagValueProcessor(e,t,r,i,a);return null==s?t:typeof s!=typeof t||s!==t?s:this.options.trimValues||t.trim()===t?j(t,this.options.parseTagValue,this.options.numberParseOptions):t}}function I(t){if(this.options.removeNSPrefix){var e=t.split(":"),r="/"===t.charAt(0)?"/":"";if("xmlns"===e[0])return"";2===e.length&&(t=r+e[1])}return t}var S=new RegExp("([^\\s=]+)\\s*(=\\s*(['\"])([\\s\\S]*?)\\3)?","gm");function A(t,e,r){if(!0!==this.options.ignoreAttributes&&"string"==typeof t){for(var n=a(t,S),i=n.length,o={},s=0;s<i;s++){var l=this.resolveNameSpace(n[s][1]);if(!this.ignoreAttributesFn(l,e)){var u=n[s][4],f=this.options.attributeNamePrefix+l;if(l.length)if(this.options.transformAttributeName&&(f=this.options.transformAttributeName(f)),"__proto__"===f&&(f="#__proto__"),void 0!==u){this.options.trimValues&&(u=u.trim()),u=this.replaceEntitiesValue(u);var d=this.options.attributeValueProcessor(l,u,e);o[f]=null==d?u:typeof d!=typeof u||d!==u?d:j(u,this.options.parseAttributeValue,this.options.numberParseOptions)}else this.options.allowBooleanAttributes&&(o[f]=!0)}}if(!Object.keys(o).length)return;if(this.options.attributesGroupName){var g={};return g[this.options.attributesGroupName]=o,g}return o}}var O=function(t){t=t.replace(/\r\n?/g,"\n");for(var e=new l("!xml"),r=e,n="",i="",a=0;a<t.length;a++)if("<"===t[a])if("/"===t[a+1]){var o=_(t,">",a,"Closing Tag is not closed."),s=t.substring(a+2,o).trim();if(this.options.removeNSPrefix){var f=s.indexOf(":");-1!==f&&(s=s.substr(f+1))}this.options.transformTagName&&(s=this.options.transformTagName(s)),r&&(n=this.saveTextToParentTag(n,r,i));var d=i.substring(i.lastIndexOf(".")+1);if(s&&-1!==this.options.unpairedTags.indexOf(s))throw new Error("Unpaired tag can not be used as closing tag: </"+s+">");var g=0;d&&-1!==this.options.unpairedTags.indexOf(d)?(g=i.lastIndexOf(".",i.lastIndexOf(".")-1),this.tagsNodeStack.pop()):g=i.lastIndexOf("."),i=i.substring(0,g),r=this.tagsNodeStack.pop(),n="",a=o}else if("?"===t[a+1]){var h=F(t,a,!1,"?>");if(!h)throw new Error("Pi Tag is not closed.");if(n=this.saveTextToParentTag(n,r,i),this.options.ignoreDeclaration&&"?xml"===h.tagName||this.options.ignorePiTags);else{var p=new l(h.tagName);p.add(this.options.textNodeName,""),h.tagName!==h.tagExp&&h.attrExpPresent&&(p[":@"]=this.buildAttributesMap(h.tagExp,i,h.tagName)),this.addChild(r,p,i,a)}a=h.closeIndex+1}else if("!--"===t.substr(a+1,3)){var c=_(t,"--\x3e",a+4,"Comment is not closed.");if(this.options.commentPropName){var v,m=t.substring(a+4,c-2);n=this.saveTextToParentTag(n,r,i),r.add(this.options.commentPropName,[(v={},v[this.options.textNodeName]=m,v)])}a=c}else if("!D"===t.substr(a+1,2)){var x=u(t,a);this.docTypeEntities=x.entities,a=x.i}else if("!["===t.substr(a+1,2)){var b=_(t,"]]>",a,"CDATA is not closed.")-2,N=t.substring(a+9,b);n=this.saveTextToParentTag(n,r,i);var E,y=this.parseTextData(N,r.tagname,i,!0,!1,!0,!0);null==y&&(y=""),this.options.cdataPropName?r.add(this.options.cdataPropName,[(E={},E[this.options.textNodeName]=N,E)]):r.add(this.options.textNodeName,y),a=b+2}else{var T=F(t,a,this.options.removeNSPrefix),w=T.tagName,I=T.rawTagName,S=T.tagExp,A=T.attrExpPresent,O=T.closeIndex;this.options.transformTagName&&(w=this.options.transformTagName(w)),r&&n&&"!xml"!==r.tagname&&(n=this.saveTextToParentTag(n,r,i,!1));var P=r;P&&-1!==this.options.unpairedTags.indexOf(P.tagname)&&(r=this.tagsNodeStack.pop(),i=i.substring(0,i.lastIndexOf("."))),w!==e.tagname&&(i+=i?"."+w:w);var C=a;if(this.isItStopNode(this.options.stopNodes,i,w)){var D="";if(S.length>0&&S.lastIndexOf("/")===S.length-1)"/"===w[w.length-1]?(w=w.substr(0,w.length-1),i=i.substr(0,i.length-1),S=w):S=S.substr(0,S.length-1),a=T.closeIndex;else if(-1!==this.options.unpairedTags.indexOf(w))a=T.closeIndex;else{var M=this.readStopNodeData(t,I,O+1);if(!M)throw new Error("Unexpected end of "+I);a=M.i,D=M.tagContent}var k=new l(w);w!==S&&A&&(k[":@"]=this.buildAttributesMap(S,i,w)),D&&(D=this.parseTextData(D,w,i,!0,A,!0,!0)),i=i.substr(0,i.lastIndexOf(".")),k.add(this.options.textNodeName,D),this.addChild(r,k,i,C)}else{if(S.length>0&&S.lastIndexOf("/")===S.length-1){"/"===w[w.length-1]?(w=w.substr(0,w.length-1),i=i.substr(0,i.length-1),S=w):S=S.substr(0,S.length-1),this.options.transformTagName&&(w=this.options.transformTagName(w));var j=new l(w);w!==S&&A&&(j[":@"]=this.buildAttributesMap(S,i,w)),this.addChild(r,j,i,C),i=i.substr(0,i.lastIndexOf("."))}else{var V=new l(w);this.tagsNodeStack.push(r),w!==S&&A&&(V[":@"]=this.buildAttributesMap(S,i,w)),this.addChild(r,V,i,C),r=V}n="",a=O}}else n+=t[a];return e.child};function P(t,e,r,n){this.options.captureMetaData||(n=void 0);var i=this.options.updateTag(e.tagname,r,e[":@"]);!1===i||("string"==typeof i?(e.tagname=i,t.addChild(e,n)):t.addChild(e,n))}var C=function(t){if(this.options.processEntities){for(var e in this.docTypeEntities){var r=this.docTypeEntities[e];t=t.replace(r.regx,r.val)}for(var n in this.lastEntities){var i=this.lastEntities[n];t=t.replace(i.regex,i.val)}if(this.options.htmlEntities)for(var a in this.htmlEntities){var o=this.htmlEntities[a];t=t.replace(o.regex,o.val)}t=t.replace(this.ampEntity.regex,this.ampEntity.val)}return t};function D(t,e,r,n){return t&&(void 0===n&&(n=0===e.child.length),void 0!==(t=this.parseTextData(t,e.tagname,r,!1,!!e[":@"]&&0!==Object.keys(e[":@"]).length,n))&&""!==t&&e.add(this.options.textNodeName,t),t=""),t}function M(t,e,r){var n="*."+r;for(var i in t){var a=t[i];if(n===a||e===a)return!0}return!1}function _(t,e,r,n){var i=t.indexOf(e,r);if(-1===i)throw new Error(n);return i+e.length-1}function F(t,e,r,n){void 0===n&&(n=">");var i=function(t,e,r){var n;void 0===r&&(r=">");for(var i="",a=e;a<t.length;a++){var o=t[a];if(n)o===n&&(n="");else if('"'===o||"'"===o)n=o;else if(o===r[0]){if(!r[1])return{data:i,index:a};if(t[a+1]===r[1])return{data:i,index:a}}else"\t"===o&&(o=" ");i+=o}}(t,e+1,n);if(i){var a=i.data,o=i.index,s=a.search(/\s/),l=a,u=!0;-1!==s&&(l=a.substring(0,s),a=a.substring(s+1).trimStart());var f=l;if(r){var d=l.indexOf(":");-1!==d&&(u=(l=l.substr(d+1))!==i.data.substr(d+1))}return{tagName:l,tagExp:a,closeIndex:o,attrExpPresent:u,rawTagName:f}}}function k(t,e,r){for(var n=r,i=1;r<t.length;r++)if("<"===t[r])if("/"===t[r+1]){var a=_(t,">",r,e+" is not closed");if(t.substring(r+2,a).trim()===e&&0==--i)return{tagContent:t.substring(n,r),i:a};r=a}else if("?"===t[r+1])r=_(t,"?>",r+1,"StopNode is not closed.");else if("!--"===t.substr(r+1,3))r=_(t,"--\x3e",r+3,"StopNode is not closed.");else if("!["===t.substr(r+1,2))r=_(t,"]]>",r,"StopNode is not closed.")-2;else{var o=F(t,r,">");o&&((o&&o.tagName)===e&&"/"!==o.tagExp[o.tagExp.length-1]&&i++,r=o.closeIndex)}}function j(t,e,r){if(e&&"string"==typeof t){var n=t.trim();return"true"===n||"false"!==n&&function(t,e={}){if(e=Object.assign({},b,e),!t||"string"!=typeof t)return t;let r=t.trim();if(void 0!==e.skipLike&&e.skipLike.test(r))return t;if("0"===t)return 0;if(e.hex&&m.test(r))return function(t){if(parseInt)return parseInt(t,16);if(Number.parseInt)return Number.parseInt(t,16);if(window&&window.parseInt)return window.parseInt(t,16);throw new Error("parseInt, Number.parseInt, window.parseInt are not supported")}(r);if(-1!==r.search(/.+[eE].+/))return function(t,e,r){if(!r.eNotation)return t;const n=e.match(N);if(n){let i=n[1]||"";const a=-1===n[3].indexOf("e")?"E":"e",o=n[2],s=i?t[o.length+1]===a:t[o.length]===a;return o.length>1&&s?t:1!==o.length||!n[3].startsWith(`.${a}`)&&n[3][0]!==a?r.leadingZeros&&!s?(e=(n[1]||"")+n[3],Number(e)):t:Number(e)}return t}(t,r,e);{const i=x.exec(r);if(i){const a=i[1]||"",o=i[2];let s=(n=i[3])&&-1!==n.indexOf(".")?("."===(n=n.replace(/0+$/,""))?n="0":"."===n[0]?n="0"+n:"."===n[n.length-1]&&(n=n.substring(0,n.length-1)),n):n;const l=a?"."===t[o.length+1]:"."===t[o.length];if(!e.leadingZeros&&(o.length>1||1===o.length&&!l))return t;{const n=Number(r),i=String(n);if(0===n||-0===n)return n;if(-1!==i.search(/[eE]/))return e.eNotation?n:t;if(-1!==r.indexOf("."))return"0"===i||i===s||i===`${a}${s}`?n:t;let l=o?s:r;return o?l===i||a+l===i?n:t:l===i||l===a+i?n:t}}return t}var n}(t,r)}return void 0!==t?t:""}var V=l.getMetaDataSymbol();function Y(t,e){return L(t,e)}function L(t,e,r){for(var n,i={},a=0;a<t.length;a++){var o,s=t[a],l=U(s);if(o=void 0===r?l:r+"."+l,l===e.textNodeName)void 0===n?n=s[l]:n+=""+s[l];else{if(void 0===l)continue;if(s[l]){var u=L(s[l],e,o),f=B(u,e);void 0!==s[V]&&(u[V]=s[V]),s[":@"]?X(u,s[":@"],o,e):1!==Object.keys(u).length||void 0===u[e.textNodeName]||e.alwaysCreateTextNode?0===Object.keys(u).length&&(e.alwaysCreateTextNode?u[e.textNodeName]="":u=""):u=u[e.textNodeName],void 0!==i[l]&&i.hasOwnProperty(l)?(Array.isArray(i[l])||(i[l]=[i[l]]),i[l].push(u)):e.isArray(l,o,f)?i[l]=[u]:i[l]=u}}}return"string"==typeof n?n.length>0&&(i[e.textNodeName]=n):void 0!==n&&(i[e.textNodeName]=n),i}function U(t){for(var e=Object.keys(t),r=0;r<e.length;r++){var n=e[r];if(":@"!==n)return n}}function X(t,e,r,n){if(e)for(var i=Object.keys(e),a=i.length,o=0;o<a;o++){var s=i[o];n.isArray(s,r+"."+s,!0,!0)?t[s]=[e[s]]:t[s]=e[s]}}function B(t,e){var r=e.textNodeName,n=Object.keys(t).length;return 0===n||!(1!==n||!t[r]&&"boolean"!=typeof t[r]&&0!==t[r])}var $={allowBooleanAttributes:!1,unpairedTags:[]};function R(t){return" "===t||"\t"===t||"\n"===t||"\r"===t}function Z(t,e){for(var r=e;e<t.length;e++)if("?"!=t[e]&&" "!=t[e]);else{var n=t.substr(r,e-r);if(e>5&&"xml"===n)return H("InvalidXml","XML declaration allowed only at the start of the document.",Q(t,e));if("?"==t[e]&&">"==t[e+1]){e++;break}}return e}function q(t,e){if(t.length>e+5&&"-"===t[e+1]&&"-"===t[e+2]){for(e+=3;e<t.length;e++)if("-"===t[e]&&"-"===t[e+1]&&">"===t[e+2]){e+=2;break}}else if(t.length>e+8&&"D"===t[e+1]&&"O"===t[e+2]&&"C"===t[e+3]&&"T"===t[e+4]&&"Y"===t[e+5]&&"P"===t[e+6]&&"E"===t[e+7]){var r=1;for(e+=8;e<t.length;e++)if("<"===t[e])r++;else if(">"===t[e]&&0==--r)break}else if(t.length>e+9&&"["===t[e+1]&&"C"===t[e+2]&&"D"===t[e+3]&&"A"===t[e+4]&&"T"===t[e+5]&&"A"===t[e+6]&&"["===t[e+7])for(e+=8;e<t.length;e++)if("]"===t[e]&&"]"===t[e+1]&&">"===t[e+2]){e+=2;break}return e}function G(t,e){for(var r="",n="",i=!1;e<t.length;e++){if('"'===t[e]||"'"===t[e])""===n?n=t[e]:n!==t[e]||(n="");else if(">"===t[e]&&""===n){i=!0;break}r+=t[e]}return""===n&&{value:r,index:e,tagClosed:i}}var z=new RegExp("(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['\"])(([\\s\\S])*?)\\5)?","g");function J(t,e){for(var r=a(t,z),n={},i=0;i<r.length;i++){if(0===r[i][1].length)return H("InvalidAttr","Attribute '"+r[i][2]+"' has no space in starting.",tt(r[i]));if(void 0!==r[i][3]&&void 0===r[i][4])return H("InvalidAttr","Attribute '"+r[i][2]+"' is without value.",tt(r[i]));if(void 0===r[i][3]&&!e.allowBooleanAttributes)return H("InvalidAttr","boolean attribute '"+r[i][2]+"' is not allowed.",tt(r[i]));var o=r[i][2];if(!K(o))return H("InvalidAttr","Attribute '"+o+"' is an invalid name.",tt(r[i]));if(n.hasOwnProperty(o))return H("InvalidAttr","Attribute '"+o+"' is repeated.",tt(r[i]));n[o]=1}return!0}function W(t,e){if(";"===t[++e])return-1;if("#"===t[e])return function(t,e){var r=/\d/;for("x"===t[e]&&(e++,r=/[\da-fA-F]/);e<t.length;e++){if(";"===t[e])return e;if(!t[e].match(r))break}return-1}(t,++e);for(var r=0;e<t.length;e++,r++)if(!(t[e].match(/\w/)&&r<20)){if(";"===t[e])break;return-1}return e}function H(t,e,r){return{err:{code:t,msg:e,line:r.line||r,col:r.col}}}function K(t){return s(t)}function Q(t,e){var r=t.substring(0,e).split(/\r?\n/);return{line:r.length,col:r[r.length-1].length+1}}function tt(t){return t.startIndex+t[1].length}var et=function(){function t(t){this.externalEntities={},this.options=function(t){return Object.assign({},r,t)}(t)}var e=t.prototype;return e.parse=function(t,e){if("string"==typeof t);else{if(!t.toString)throw new Error("XML data is accepted in String or Bytes[] form.");t=t.toString()}if(e){!0===e&&(e={});var r=function(t,e){e=Object.assign({},$,e);var r=[],n=!1,i=!1;"\ufeff"===t[0]&&(t=t.substr(1));for(var a=0;a<t.length;a++)if("<"===t[a]&&"?"===t[a+1]){if((a=Z(t,a+=2)).err)return a}else{if("<"!==t[a]){if(R(t[a]))continue;return H("InvalidChar","char '"+t[a]+"' is not expected.",Q(t,a))}var o=a;if("!"===t[++a]){a=q(t,a);continue}var l=!1;"/"===t[a]&&(l=!0,a++);for(var u="";a<t.length&&">"!==t[a]&&" "!==t[a]&&"\t"!==t[a]&&"\n"!==t[a]&&"\r"!==t[a];a++)u+=t[a];if("/"===(u=u.trim())[u.length-1]&&(u=u.substring(0,u.length-1),a--),!s(u))return H("InvalidTag",0===u.trim().length?"Invalid space after '<'.":"Tag '"+u+"' is an invalid name.",Q(t,a));var f=G(t,a);if(!1===f)return H("InvalidAttr","Attributes for '"+u+"' have open quote.",Q(t,a));var d=f.value;if(a=f.index,"/"===d[d.length-1]){var g=a-d.length,h=J(d=d.substring(0,d.length-1),e);if(!0!==h)return H(h.err.code,h.err.msg,Q(t,g+h.err.line));n=!0}else if(l){if(!f.tagClosed)return H("InvalidTag","Closing tag '"+u+"' doesn't have proper closing.",Q(t,a));if(d.trim().length>0)return H("InvalidTag","Closing tag '"+u+"' can't have attributes or invalid starting.",Q(t,o));if(0===r.length)return H("InvalidTag","Closing tag '"+u+"' has not been opened.",Q(t,o));var p=r.pop();if(u!==p.tagName){var c=Q(t,p.tagStartPos);return H("InvalidTag","Expected closing tag '"+p.tagName+"' (opened in line "+c.line+", col "+c.col+") instead of closing tag '"+u+"'.",Q(t,o))}0==r.length&&(i=!0)}else{var v=J(d,e);if(!0!==v)return H(v.err.code,v.err.msg,Q(t,a-d.length+v.err.line));if(!0===i)return H("InvalidXml","Multiple possible root nodes found.",Q(t,a));-1!==e.unpairedTags.indexOf(u)||r.push({tagName:u,tagStartPos:o}),n=!0}for(a++;a<t.length;a++)if("<"===t[a]){if("!"===t[a+1]){a=q(t,++a);continue}if("?"!==t[a+1])break;if((a=Z(t,++a)).err)return a}else if("&"===t[a]){var m=W(t,a);if(-1==m)return H("InvalidChar","char '&' is not expected.",Q(t,a));a=m}else if(!0===i&&!R(t[a]))return H("InvalidXml","Extra text at the end",Q(t,a));"<"===t[a]&&a--}return n?1==r.length?H("InvalidTag","Unclosed tag '"+r[0].tagName+"'.",Q(t,r[0].tagStartPos)):!(r.length>0)||H("InvalidXml","Invalid '"+JSON.stringify(r.map((function(t){return t.tagName})),null,4).replace(/\r?\n/g,"")+"' found.",{line:1,col:1}):H("InvalidXml","Start tag expected.",1)}(t,e);if(!0!==r)throw Error(r.err.msg+":"+r.err.line+":"+r.err.col)}var n=new y(this.options);n.addExternalEntities(this.externalEntities);var i=n.parseXml(t);return this.options.preserveOrder||void 0===i?i:Y(i,this.options)},e.addEntity=function(t,e){if(-1!==e.indexOf("&"))throw new Error("Entity value can't have '&'");if(-1!==t.indexOf("&")||-1!==t.indexOf(";"))throw new Error("An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'");if("&"===e)throw new Error("An entity with value '&' is not permitted");this.externalEntities[t]=e},t.getMetaDataSymbol=function(){return l.getMetaDataSymbol()},t}();return e})()));
//# sourceMappingURL=fxparser.min.js.map