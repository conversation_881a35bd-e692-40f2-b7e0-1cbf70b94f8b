!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.XMLBuilder=e():t.XMLBuilder=e()}(this,(()=>(()=>{"use strict";var t={d:(e,i)=>{for(var r in i)t.o(i,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:i[r]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};function i(t,e){var i="";return e.format&&e.indentBy.length>0&&(i="\n"),r(t,e,"",i)}function r(t,e,i,u){for(var p="",h=!1,l=0;l<t.length;l++){var d=t[l],f=n(d);if(void 0!==f){var c;if(c=0===i.length?f:i+"."+f,f!==e.textNodeName)if(f!==e.cdataPropName)if(f!==e.commentPropName)if("?"!==f[0]){var g=u;""!==g&&(g+=e.indentBy);var b=u+"<"+f+o(d[":@"],e),m=r(d[f],e,c,g);-1!==e.unpairedTags.indexOf(f)?e.suppressUnpairedNode?p+=b+">":p+=b+"/>":m&&0!==m.length||!e.suppressEmptyNode?m&&m.endsWith(">")?p+=b+">"+m+u+"</"+f+">":(p+=b+">",m&&""!==u&&(m.includes("/>")||m.includes("</"))?p+=u+e.indentBy+m+u:p+=m,p+="</"+f+">"):p+=b+"/>",h=!0}else{var N=o(d[":@"],e),v="?xml"===f?"":u,y=d[f][0][e.textNodeName];p+=v+"<"+f+(y=0!==y.length?" "+y:"")+N+"?>",h=!0}else p+=u+"\x3c!--"+d[f][0][e.textNodeName]+"--\x3e",h=!0;else h&&(p+=u),p+="<![CDATA["+d[f][0][e.textNodeName]+"]]>",h=!1;else{var x=d[f];s(c,e)||(x=a(x=e.tagValueProcessor(f,x),e)),h&&(p+=u),p+=x,h=!1}}}return p}function n(t){for(var e=Object.keys(t),i=0;i<e.length;i++){var r=e[i];if(t.hasOwnProperty(r)&&":@"!==r)return r}}function o(t,e){var i="";if(t&&!e.ignoreAttributes)for(var r in t)if(t.hasOwnProperty(r)){var n=e.attributeValueProcessor(r,t[r]);!0===(n=a(n,e))&&e.suppressBooleanAttributes?i+=" "+r.substr(e.attributeNamePrefix.length):i+=" "+r.substr(e.attributeNamePrefix.length)+'="'+n+'"'}return i}function s(t,e){var i=(t=t.substr(0,t.length-e.textNodeName.length-1)).substr(t.lastIndexOf(".")+1);for(var r in e.stopNodes)if(e.stopNodes[r]===t||e.stopNodes[r]==="*."+i)return!0;return!1}function a(t,e){if(t&&t.length>0&&e.processEntities)for(var i=0;i<e.entities.length;i++){var r=e.entities[i];t=t.replace(r.regex,r.val)}return t}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,r=Array(e);i<e;i++)r[i]=t[i];return r}t.r(e),t.d(e,{default:()=>h});var p={attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,cdataPropName:!1,format:!1,indentBy:"  ",suppressEmptyNode:!1,suppressUnpairedNode:!0,suppressBooleanAttributes:!0,tagValueProcessor:function(t,e){return e},attributeValueProcessor:function(t,e){return e},preserveOrder:!1,commentPropName:!1,unpairedTags:[],entities:[{regex:new RegExp("&","g"),val:"&amp;"},{regex:new RegExp(">","g"),val:"&gt;"},{regex:new RegExp("<","g"),val:"&lt;"},{regex:new RegExp("'","g"),val:"&apos;"},{regex:new RegExp('"',"g"),val:"&quot;"}],processEntities:!0,stopNodes:[],oneListGroup:!1};function h(t){var e;this.options=Object.assign({},p,t),!0===this.options.ignoreAttributes||this.options.attributesGroupName?this.isAttribute=function(){return!1}:(this.ignoreAttributesFn="function"==typeof(e=this.options.ignoreAttributes)?e:Array.isArray(e)?function(t){for(var i,r=function(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(i)return(i=i.call(t)).next.bind(i);if(Array.isArray(t)||(i=function(t,e){if(t){if("string"==typeof t)return u(t,e);var i={}.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?u(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(e);!(i=r()).done;){var n=i.value;if("string"==typeof n&&t===n)return!0;if(n instanceof RegExp&&n.test(t))return!0}}:function(){return!1},this.attrPrefixLen=this.options.attributeNamePrefix.length,this.isAttribute=f),this.processTextOrObjNode=l,this.options.format?(this.indentate=d,this.tagEndChar=">\n",this.newLine="\n"):(this.indentate=function(){return""},this.tagEndChar=">",this.newLine="")}function l(t,e,i,r){var n=this.j2x(t,i+1,r.concat(e));return void 0!==t[this.options.textNodeName]&&1===Object.keys(t).length?this.buildTextValNode(t[this.options.textNodeName],e,n.attrStr,i):this.buildObjectNode(n.val,e,n.attrStr,i)}function d(t){return this.options.indentBy.repeat(t)}function f(t){return!(!t.startsWith(this.options.attributeNamePrefix)||t===this.options.textNodeName)&&t.substr(this.attrPrefixLen)}return h.prototype.build=function(t){return this.options.preserveOrder?i(t,this.options):(Array.isArray(t)&&this.options.arrayNodeName&&this.options.arrayNodeName.length>1&&((e={})[this.options.arrayNodeName]=t,t=e),this.j2x(t,0,[]).val);var e},h.prototype.j2x=function(t,e,i){var r="",n="",o=i.join(".");for(var s in t)if(Object.prototype.hasOwnProperty.call(t,s))if(void 0===t[s])this.isAttribute(s)&&(n+="");else if(null===t[s])this.isAttribute(s)||s===this.options.cdataPropName?n+="":"?"===s[0]?n+=this.indentate(e)+"<"+s+"?"+this.tagEndChar:n+=this.indentate(e)+"<"+s+"/"+this.tagEndChar;else if(t[s]instanceof Date)n+=this.buildTextValNode(t[s],s,"",e);else if("object"!=typeof t[s]){var a=this.isAttribute(s);if(a&&!this.ignoreAttributesFn(a,o))r+=this.buildAttrPairStr(a,""+t[s]);else if(!a)if(s===this.options.textNodeName){var u=this.options.tagValueProcessor(s,""+t[s]);n+=this.replaceEntitiesValue(u)}else n+=this.buildTextValNode(t[s],s,"",e)}else if(Array.isArray(t[s])){for(var p=t[s].length,h="",l="",d=0;d<p;d++){var f=t[s][d];if(void 0===f);else if(null===f)"?"===s[0]?n+=this.indentate(e)+"<"+s+"?"+this.tagEndChar:n+=this.indentate(e)+"<"+s+"/"+this.tagEndChar;else if("object"==typeof f)if(this.options.oneListGroup){var c=this.j2x(f,e+1,i.concat(s));h+=c.val,this.options.attributesGroupName&&f.hasOwnProperty(this.options.attributesGroupName)&&(l+=c.attrStr)}else h+=this.processTextOrObjNode(f,s,e,i);else if(this.options.oneListGroup){var g=this.options.tagValueProcessor(s,f);h+=g=this.replaceEntitiesValue(g)}else h+=this.buildTextValNode(f,s,"",e)}this.options.oneListGroup&&(h=this.buildObjectNode(h,s,l,e)),n+=h}else if(this.options.attributesGroupName&&s===this.options.attributesGroupName)for(var b=Object.keys(t[s]),m=b.length,N=0;N<m;N++)r+=this.buildAttrPairStr(b[N],""+t[s][b[N]]);else n+=this.processTextOrObjNode(t[s],s,e,i);return{attrStr:r,val:n}},h.prototype.buildAttrPairStr=function(t,e){return e=this.options.attributeValueProcessor(t,""+e),e=this.replaceEntitiesValue(e),this.options.suppressBooleanAttributes&&"true"===e?" "+t:" "+t+'="'+e+'"'},h.prototype.buildObjectNode=function(t,e,i,r){if(""===t)return"?"===e[0]?this.indentate(r)+"<"+e+i+"?"+this.tagEndChar:this.indentate(r)+"<"+e+i+this.closeTag(e)+this.tagEndChar;var n="</"+e+this.tagEndChar,o="";return"?"===e[0]&&(o="?",n=""),!i&&""!==i||-1!==t.indexOf("<")?!1!==this.options.commentPropName&&e===this.options.commentPropName&&0===o.length?this.indentate(r)+"\x3c!--"+t+"--\x3e"+this.newLine:this.indentate(r)+"<"+e+i+o+this.tagEndChar+t+this.indentate(r)+n:this.indentate(r)+"<"+e+i+o+">"+t+n},h.prototype.closeTag=function(t){var e="";return-1!==this.options.unpairedTags.indexOf(t)?this.options.suppressUnpairedNode||(e="/"):e=this.options.suppressEmptyNode?"/":"></"+t,e},h.prototype.buildTextValNode=function(t,e,i,r){if(!1!==this.options.cdataPropName&&e===this.options.cdataPropName)return this.indentate(r)+"<![CDATA["+t+"]]>"+this.newLine;if(!1!==this.options.commentPropName&&e===this.options.commentPropName)return this.indentate(r)+"\x3c!--"+t+"--\x3e"+this.newLine;if("?"===e[0])return this.indentate(r)+"<"+e+i+"?"+this.tagEndChar;var n=this.options.tagValueProcessor(e,t);return""===(n=this.replaceEntitiesValue(n))?this.indentate(r)+"<"+e+i+this.closeTag(e)+this.tagEndChar:this.indentate(r)+"<"+e+i+">"+n+"</"+e+this.tagEndChar},h.prototype.replaceEntitiesValue=function(t){if(t&&t.length>0&&this.options.processEntities)for(var e=0;e<this.options.entities.length;e++){var i=this.options.entities[e];t=t.replace(i.regex,i.val)}return t},e})()));
//# sourceMappingURL=fxbuilder.min.js.map