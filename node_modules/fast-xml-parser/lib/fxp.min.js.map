{"version": 3, "file": "./lib/fxp.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAa,IAAID,IAEjBD,EAAU,IAAIC,GACf,CATD,CASGK,MAAM,I,mBCRT,IAAIC,EAAsB,CCA1BA,EAAwB,CAACL,EAASM,KACjC,IAAI,IAAIC,KAAOD,EACXD,EAAoBG,EAAEF,EAAYC,KAASF,EAAoBG,EAAER,EAASO,IAC5EE,OAAOC,eAAeV,EAASO,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDF,EAAwB,CAACQ,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFT,EAAyBL,IACH,oBAAXkB,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeV,EAASkB,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeV,EAAS,aAAc,CAAEoB,OAAO,GAAO,G,4ECH9D,IAAMC,EAAgB,gLAGhBC,EAAY,IAAIC,OAAO,KADGF,EAAgB,KAD/BA,EAEY,mDAEtB,SAASG,EAAcC,EAAQC,GAGpC,IAFA,IAAMC,EAAU,GACZC,EAAQF,EAAMG,KAAKJ,GAChBG,GAAO,CACZ,IAAME,EAAa,GACnBA,EAAWC,WAAaL,EAAMM,UAAYJ,EAAM,GAAGK,OAEnD,IADA,IAAMC,EAAMN,EAAMK,OACTE,EAAQ,EAAGA,EAAQD,EAAKC,IAC/BL,EAAWM,KAAKR,EAAMO,IAExBR,EAAQS,KAAKN,GACbF,EAAQF,EAAMG,KAAKJ,EACrB,CACA,OAAOE,CACT,CAEO,IAAMU,EAAS,SAASZ,GAE7B,QAAQ,MADMH,EAAUO,KAAKJ,GAE/B,ECtBMa,EAAiB,CACrBC,wBAAwB,EACxBC,aAAc,IAIT,SAASC,EAASC,EAASC,GAChCA,EAAUlC,OAAOmC,OAAO,CAAC,EAAGN,EAAgBK,GAK5C,IAAME,EAAO,GACTC,GAAW,EAGXC,GAAc,EAEC,WAAfL,EAAQ,KAEVA,EAAUA,EAAQM,OAAO,IAG3B,IAAK,IAAIC,EAAI,EAAGA,EAAIP,EAAQT,OAAQgB,IAElC,GAAmB,MAAfP,EAAQO,IAA+B,MAAjBP,EAAQO,EAAE,IAGlC,IADAA,EAAIC,EAAOR,EADXO,GAAG,IAEGE,IAAK,OAAOF,MACd,IAAmB,MAAfP,EAAQO,GA0IX,CACL,GAAKG,EAAaV,EAAQO,IACxB,SAEF,OAAOI,EAAe,cAAe,SAASX,EAAQO,GAAG,qBAAsBK,EAAyBZ,EAASO,GACnH,CA5IE,IAAIM,EAAcN,EAGlB,GAAmB,MAAfP,IAFJO,GAEwB,CACtBA,EAAIO,EAAoBd,EAASO,GACjC,QACF,CACE,IAAIQ,GAAa,EACE,MAAff,EAAQO,KAEVQ,GAAa,EACbR,KAIF,IADA,IAAIS,EAAU,GACPT,EAAIP,EAAQT,QACF,MAAfS,EAAQO,IACO,MAAfP,EAAQO,IACO,OAAfP,EAAQO,IACO,OAAfP,EAAQO,IACO,OAAfP,EAAQO,GAAaA,IAErBS,GAAWhB,EAAQO,GAWrB,GANoC,OAHpCS,EAAUA,EAAQC,QAGND,EAAQzB,OAAS,KAE3ByB,EAAUA,EAAQE,UAAU,EAAGF,EAAQzB,OAAS,GAEhDgB,MAoVDZ,EAlVoBqB,GAOnB,OAAOL,EAAe,aALQ,IAA1BK,EAAQC,OAAO1B,OACX,2BAEA,QAAQyB,EAAQ,wBAEiBJ,EAAyBZ,EAASO,IAG7E,IAAMY,EAASC,EAAiBpB,EAASO,GACzC,IAAe,IAAXY,EACF,OAAOR,EAAe,cAAe,mBAAmBK,EAAQ,qBAAsBJ,EAAyBZ,EAASO,IAE1H,IAAIc,EAAUF,EAAOzC,MAGrB,GAFA6B,EAAIY,EAAO1B,MAEyB,MAAhC4B,EAAQA,EAAQ9B,OAAS,GAAY,CAEvC,IAAM+B,EAAef,EAAIc,EAAQ9B,OAE3BgC,EAAUC,EADhBH,EAAUA,EAAQH,UAAU,EAAGG,EAAQ9B,OAAS,GACCU,GACjD,IAAgB,IAAZsB,EAOF,OAAOZ,EAAeY,EAAQd,IAAIgB,KAAMF,EAAQd,IAAIiB,IAAKd,EAAyBZ,EAASsB,EAAeC,EAAQd,IAAIkB,OANtHvB,GAAW,CAQf,MAAO,GAAIW,EAAY,CACrB,IAAKI,EAAOS,UACV,OAAOjB,EAAe,aAAc,gBAAgBK,EAAQ,iCAAkCJ,EAAyBZ,EAASO,IAC3H,GAAIc,EAAQJ,OAAO1B,OAAS,EACjC,OAAOoB,EAAe,aAAc,gBAAgBK,EAAQ,+CAAgDJ,EAAyBZ,EAASa,IACzI,GAAoB,IAAhBV,EAAKZ,OACd,OAAOoB,EAAe,aAAc,gBAAgBK,EAAQ,yBAA0BJ,EAAyBZ,EAASa,IAExH,IAAMgB,EAAM1B,EAAK2B,MACjB,GAAId,IAAYa,EAAIb,QAAS,CAC3B,IAAIe,EAAUnB,EAAyBZ,EAAS6B,EAAIhB,aACpD,OAAOF,EAAe,aACpB,yBAAyBkB,EAAIb,QAAQ,qBAAqBe,EAAQJ,KAAK,SAASI,EAAQC,IAAI,6BAA6BhB,EAAQ,KACjIJ,EAAyBZ,EAASa,GACtC,CAGmB,GAAfV,EAAKZ,SACPc,GAAc,EAGpB,KAAO,CACL,IAAMkB,EAAUC,EAAwBH,EAASpB,GACjD,IAAgB,IAAZsB,EAIF,OAAOZ,EAAeY,EAAQd,IAAIgB,KAAMF,EAAQd,IAAIiB,IAAKd,EAAyBZ,EAASO,EAAIc,EAAQ9B,OAASgC,EAAQd,IAAIkB,OAI9H,IAAoB,IAAhBtB,EACF,OAAOM,EAAe,aAAc,sCAAuCC,EAAyBZ,EAASO,KAC1D,IAA3CN,EAAQH,aAAamC,QAAQjB,IAGrCb,EAAKT,KAAK,CAACsB,QAAAA,EAASH,YAAAA,IAEtBT,GAAW,CACb,CAIA,IAAKG,IAAKA,EAAIP,EAAQT,OAAQgB,IAC5B,GAAmB,MAAfP,EAAQO,GAAY,CACtB,GAAuB,MAAnBP,EAAQO,EAAI,GAAY,CAG1BA,EAAIO,EAAoBd,IADxBO,GAEA,QACF,CAAO,GAAqB,MAAjBP,EAAQO,EAAE,GAInB,MAFA,IADAA,EAAIC,EAAOR,IAAWO,IAChBE,IAAK,OAAOF,CAItB,MAAO,GAAmB,MAAfP,EAAQO,GAAY,CAC7B,IAAM2B,EAAWC,EAAkBnC,EAASO,GAC5C,IAAiB,GAAb2B,EACF,OAAOvB,EAAe,cAAe,4BAA6BC,EAAyBZ,EAASO,IACtGA,EAAI2B,CACN,MACE,IAAoB,IAAhB7B,IAAyBK,EAAaV,EAAQO,IAChD,OAAOI,EAAe,aAAc,wBAAyBC,EAAyBZ,EAASO,IAIlF,MAAfP,EAAQO,IACVA,GAQN,CAGF,OAAKH,EAEoB,GAAfD,EAAKZ,OACJoB,EAAe,aAAc,iBAAiBR,EAAK,GAAGa,QAAQ,KAAMJ,EAAyBZ,EAASG,EAAK,GAAGU,gBAC/GV,EAAKZ,OAAS,IACboB,EAAe,aAAc,YAChCyB,KAAKC,UAAUlC,EAAKmC,KAAI,SAAAC,GAAC,OAAIA,EAAEvB,OAAO,IAAG,KAAM,GAAGwB,QAAQ,SAAU,IACpE,WAAY,CAACb,KAAM,EAAGK,IAAK,IAN1BrB,EAAe,aAAc,sBAAuB,EAU/D,CAEA,SAASD,EAAa+B,GACpB,MAAgB,MAATA,GAAyB,OAATA,GAA0B,OAATA,GAA2B,OAATA,CAC5D,CAMA,SAASjC,EAAOR,EAASO,GAEvB,IADA,IAAMmC,EAAQnC,EACPA,EAAIP,EAAQT,OAAQgB,IACzB,GAAkB,KAAdP,EAAQO,IAA2B,KAAdP,EAAQO,QAAjC,CAEE,IAAMoC,EAAU3C,EAAQM,OAAOoC,EAAOnC,EAAImC,GAC1C,GAAInC,EAAI,GAAiB,QAAZoC,EACX,OAAOhC,EAAe,aAAc,6DAA8DC,EAAyBZ,EAASO,IAC/H,GAAkB,KAAdP,EAAQO,IAA+B,KAAlBP,EAAQO,EAAI,GAAW,CAErDA,IACA,KACF,CAGF,CAEF,OAAOA,CACT,CAEA,SAASO,EAAoBd,EAASO,GACpC,GAAIP,EAAQT,OAASgB,EAAI,GAAwB,MAAnBP,EAAQO,EAAI,IAAiC,MAAnBP,EAAQO,EAAI,IAElE,IAAKA,GAAK,EAAGA,EAAIP,EAAQT,OAAQgB,IAC/B,GAAmB,MAAfP,EAAQO,IAAiC,MAAnBP,EAAQO,EAAI,IAAiC,MAAnBP,EAAQO,EAAI,GAAY,CAC1EA,GAAK,EACL,KACF,OAEG,GACLP,EAAQT,OAASgB,EAAI,GACF,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,GACZ,CACA,IAAIqC,EAAqB,EACzB,IAAKrC,GAAK,EAAGA,EAAIP,EAAQT,OAAQgB,IAC/B,GAAmB,MAAfP,EAAQO,GACVqC,SACK,GAAmB,MAAf5C,EAAQO,IAEU,KAD3BqC,EAEE,KAIR,MAAO,GACL5C,EAAQT,OAASgB,EAAI,GACF,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,GAEZ,IAAKA,GAAK,EAAGA,EAAIP,EAAQT,OAAQgB,IAC/B,GAAmB,MAAfP,EAAQO,IAAiC,MAAnBP,EAAQO,EAAI,IAAiC,MAAnBP,EAAQO,EAAI,GAAY,CAC1EA,GAAK,EACL,KACF,CAIJ,OAAOA,CACT,CAEA,IAAMsC,EAAc,IACdC,EAAc,IAOpB,SAAS1B,EAAiBpB,EAASO,GAIjC,IAHA,IAAIc,EAAU,GACV0B,EAAY,GACZnB,GAAY,EACTrB,EAAIP,EAAQT,OAAQgB,IAAK,CAC9B,GAAIP,EAAQO,KAAOsC,GAAe7C,EAAQO,KAAOuC,EAC7B,KAAdC,EACFA,EAAY/C,EAAQO,GACXwC,IAAc/C,EAAQO,KAG/BwC,EAAY,SAET,GAAmB,MAAf/C,EAAQO,IACC,KAAdwC,EAAkB,CACpBnB,GAAY,EACZ,KACF,CAEFP,GAAWrB,EAAQO,EACrB,CACA,MAAkB,KAAdwC,GAIG,CACLrE,MAAO2C,EACP5B,MAAOc,EACPqB,UAAWA,EAEf,CAKA,IAAMoB,EAAoB,IAAInE,OAAO,0DAA2D,KAIhG,SAAS2C,EAAwBH,EAASpB,GAQxC,IAHA,IAAMhB,EAAUH,EAAcuC,EAAS2B,GACjCC,EAAY,CAAC,EAEV1C,EAAI,EAAGA,EAAItB,EAAQM,OAAQgB,IAAK,CACvC,GAA6B,IAAzBtB,EAAQsB,GAAG,GAAGhB,OAEhB,OAAOoB,EAAe,cAAe,cAAc1B,EAAQsB,GAAG,GAAG,8BAA+B2C,EAAqBjE,EAAQsB,KACxH,QAAsB4C,IAAlBlE,EAAQsB,GAAG,SAAsC4C,IAAlBlE,EAAQsB,GAAG,GACnD,OAAOI,EAAe,cAAe,cAAc1B,EAAQsB,GAAG,GAAG,sBAAuB2C,EAAqBjE,EAAQsB,KAChH,QAAsB4C,IAAlBlE,EAAQsB,GAAG,KAAqBN,EAAQJ,uBAEjD,OAAOc,EAAe,cAAe,sBAAsB1B,EAAQsB,GAAG,GAAG,oBAAqB2C,EAAqBjE,EAAQsB,KAK7H,IAAM6C,EAAWnE,EAAQsB,GAAG,GAC5B,IAAK8C,EAAiBD,GACpB,OAAOzC,EAAe,cAAe,cAAcyC,EAAS,wBAAyBF,EAAqBjE,EAAQsB,KAEpH,GAAK0C,EAAU3E,eAAe8E,GAI5B,OAAOzC,EAAe,cAAe,cAAcyC,EAAS,iBAAkBF,EAAqBjE,EAAQsB,KAF3G0C,EAAUG,GAAY,CAI1B,CAEA,OAAO,CACT,CAiBA,SAASjB,EAAkBnC,EAASO,GAGlC,GAAmB,MAAfP,IADJO,GAEE,OAAQ,EACV,GAAmB,MAAfP,EAAQO,GAEV,OAtBJ,SAAiCP,EAASO,GACxC,IAAI+C,EAAK,KAKT,IAJmB,MAAftD,EAAQO,KACVA,IACA+C,EAAK,cAEA/C,EAAIP,EAAQT,OAAQgB,IAAK,CAC9B,GAAmB,MAAfP,EAAQO,GACV,OAAOA,EACT,IAAKP,EAAQO,GAAGrB,MAAMoE,GACpB,KACJ,CACA,OAAQ,CACV,CASWC,CAAwBvD,IAD/BO,GAIF,IADA,IAAIiD,EAAQ,EACLjD,EAAIP,EAAQT,OAAQgB,IAAKiD,IAC9B,KAAIxD,EAAQO,GAAGrB,MAAM,OAASsE,EAAQ,IAAtC,CAEA,GAAmB,MAAfxD,EAAQO,GACV,MACF,OAAQ,CAHE,CAKZ,OAAOA,CACT,CAEA,SAASI,EAAec,EAAMgC,EAASC,GACrC,MAAO,CACLjD,IAAK,CACHgB,KAAMA,EACNC,IAAK+B,EACL9B,KAAM+B,EAAW/B,MAAQ+B,EACzB1B,IAAK0B,EAAW1B,KAGtB,CAEA,SAASqB,EAAiBD,GACxB,OAAOzD,EAAOyD,EAChB,CASA,SAASxC,EAAyBZ,EAASP,GACzC,IAAMkE,EAAQ3D,EAAQkB,UAAU,EAAGzB,GAAOmE,MAAM,SAChD,MAAO,CACLjC,KAAMgC,EAAMpE,OAGZyC,IAAK2B,EAAMA,EAAMpE,OAAS,GAAGA,OAAS,EAE1C,CAGA,SAAS2D,EAAqBhE,GAC5B,OAAOA,EAAMG,WAAaH,EAAM,GAAGK,MACrC,CCvaO,ICCHsE,EDDSjE,EAAiB,CAC1BkE,eAAe,EACfC,oBAAqB,KACrBC,qBAAqB,EACrBC,aAAc,QACdC,kBAAkB,EAClBC,gBAAgB,EAChBtE,wBAAwB,EAExBuE,eAAe,EACfC,qBAAqB,EACrBC,YAAY,EACZC,eAAe,EACfC,mBAAoB,CAClBC,KAAK,EACLC,cAAc,EACdC,WAAW,GAEbC,kBAAmB,SAAS5D,EAAS6D,GACnC,OAAOA,CACT,EACAC,wBAAyB,SAAS1B,EAAUyB,GAC1C,OAAOA,CACT,EACAE,UAAW,GACXC,sBAAsB,EACtBC,QAAS,WAAF,OAAQ,CAAK,EACpBC,iBAAiB,EACjBpF,aAAc,GACdqF,iBAAiB,EACjBC,cAAc,EACdC,mBAAmB,EACnBC,cAAc,EACdC,kBAAkB,EAClBC,wBAAwB,EACxBC,UAAW,SAASzE,EAAS0E,EAAOC,GAClC,OAAO3E,CACT,EAEA4E,iBAAiB,GCnCnB/B,EADoB,mBAAXrF,OACS,gBAEAA,OAAO,qBAC1B,IAEoBqH,EAAO,WAC1B,SAAAA,EAAYlD,GACVjF,KAAKiF,QAAUA,EACfjF,KAAKoI,MAAQ,GACbpI,KAAK,MAAQ,CAAC,CAChB,CAAC,IAAAqI,EAAAF,EAAAxH,UAuBA,OAvBA0H,EACDC,IAAA,SAAInI,EAAIgH,GAAK,IAADoB,EAEC,cAARpI,IAAqBA,EAAM,cAC9BH,KAAKoI,MAAMpG,OAAIuG,EAAA,IAAIpI,GAAMgH,EAAGoB,GAC9B,EAACF,EACDG,SAAA,SAASC,EAAM9G,GAEwC,IAAD+G,EAE/CC,EAHe,cAAjBF,EAAKxD,UAAyBwD,EAAKxD,QAAU,cAC7CwD,EAAK,OAASpI,OAAOuI,KAAKH,EAAK,OAAO5G,OAAS,EAChD7B,KAAKoI,MAAMpG,OAAI0G,EAAA,IAAKD,EAAKxD,SAAUwD,EAAKL,MAAKM,EAAG,MAAOD,EAAK,MAAKC,IAEjE1I,KAAKoI,MAAMpG,OAAI2G,EAAA,IAAKF,EAAKxD,SAAUwD,EAAKL,MAAKO,SAG5BlD,IAAf9D,IAGF3B,KAAKoI,MAAMpI,KAAKoI,MAAMvG,OAAS,GAAGsE,GAAmB,CAAExE,WAAAA,GAE3D,EACAwG,EACOU,kBAAP,WACE,OAAO1C,CACT,EAACgC,CAAA,CA5ByB,GCPb,SAASW,EAAYxG,EAASO,GAEzC,IAAMkG,EAAW,CAAC,EAClB,GAAuB,MAAnBzG,EAAQO,EAAI,IACQ,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,GAyDb,MAAM,IAAImG,MAAM,kCAvDhBnG,GAAM,EAIN,IAHA,IAAIqC,EAAqB,EACrB+D,GAAU,EAAOC,GAAU,EAE1BrG,EAAEP,EAAQT,OAAOgB,IAClB,GAAmB,MAAfP,EAAQO,IAAeqG,EA4BpB,GAAmB,MAAf5G,EAAQO,IASf,GARGqG,EACwB,MAAnB5G,EAAQO,EAAI,IAAiC,MAAnBP,EAAQO,EAAI,KACtCqG,GAAU,EACVhE,KAGJA,IAEuB,IAAvBA,EACF,UAEmB,MAAf5C,EAAQO,GACdoG,GAAU,EAEH3G,EAAQO,OA3CiB,CAChC,GAAIoG,GAAWE,EAAO7G,EAAS,UAAUO,GAAG,CAExC,IAAIuG,EAAYjC,OAAG,EAACkC,EACEC,EAAchH,GAFpCO,GAAK,GAEyC,GAA7CuG,EAAUC,EAAA,GAAElC,EAAGkC,EAAA,GAACxG,EAACwG,EAAA,IACO,IAAtBlC,EAAI5C,QAAQ,OACXwE,EAAUK,GAAe,CACrBG,KAAOpI,OAAO,IAAKiI,EAAU,IAAI,KACjCjC,IAAKA,GAEjB,MACK,GAAI8B,GAAWE,EAAO7G,EAAS,WAAWO,GAG3CA,EADgB2G,EAAelH,GAD/BO,GAAK,GACoC,GAAlCd,WAEL,GAAIkH,GAAWE,EAAO7G,EAAS,WAAWO,GAC5CA,GAAK,OAGH,GAAIoG,GAAWE,EAAO7G,EAAS,YAAYO,GAG7CA,EADgB4G,EAAgBnH,GADhCO,GAAK,GACqC,GAAnCd,UAEL,KAAIoH,EAAO7G,EAAS,MAAMO,GAC3B,MAAM,IAAImG,MAAM,mBADgBE,GAAU,CACR,CAEvChE,GAEJ,CAkBJ,GAA0B,IAAvBA,EACC,MAAM,IAAI8D,MAAM,oBAKxB,MAAO,CAACD,SAAAA,EAAUlG,EAAAA,EACtB,CAEA,IAAM6G,EAAiB,SAACC,EAAM5H,GAC1B,KAAOA,EAAQ4H,EAAK9H,QAAU,KAAK+H,KAAKD,EAAK5H,KACzCA,IAEJ,OAAOA,CACX,EAEA,SAASuH,EAAchH,EAASO,GAW5BA,EAAI6G,EAAepH,EAASO,GAI5B,IADA,IAAIuG,EAAa,GACVvG,EAAIP,EAAQT,SAAW,KAAK+H,KAAKtH,EAAQO,KAAsB,MAAfP,EAAQO,IAA6B,MAAfP,EAAQO,IACjFuG,GAAc9G,EAAQO,GACtBA,IAQJ,GANAgH,EAAmBT,GAGnBvG,EAAI6G,EAAepH,EAASO,GAGsB,WAA9CP,EAAQkB,UAAUX,EAAGA,EAAI,GAAGiH,cAC5B,MAAM,IAAId,MAAM,uCACd,GAAmB,MAAf1G,EAAQO,GACd,MAAM,IAAImG,MAAM,wCAIpB,IAAqBe,EACFC,EAAkB1H,EAASO,EAAG,UAEjD,OAFCA,EAACkH,EAAA,GAEK,CAACX,EAFOW,EAAA,KACflH,EAEJ,CAEA,SAAS4G,EAAgBnH,EAASO,GAE9BA,EAAI6G,EAAepH,EAASO,GAI5B,IADA,IAAIoH,EAAe,GACZpH,EAAIP,EAAQT,SAAW,KAAK+H,KAAKtH,EAAQO,KAC5CoH,GAAgB3H,EAAQO,GACxBA,IAEJgH,EAAmBI,GAGnBpH,EAAI6G,EAAepH,EAASO,GAG5B,IAAMqH,EAAiB5H,EAAQkB,UAAUX,EAAGA,EAAI,GAAGiH,cACnD,GAAuB,WAAnBI,GAAkD,WAAnBA,EAC/B,MAAM,IAAIlB,MAAM,qCAAqCkB,EAAc,KAEvErH,GAAKqH,EAAerI,OAGpBgB,EAAI6G,EAAepH,EAASO,GAG5B,IAAIsH,EAAmB,KACnBC,EAAmB,KAEvB,GAAuB,WAAnBF,EAA6B,CAG7B,IAAAG,EAFyBL,EAAkB1H,EAASO,EAAG,oBAMvD,GANCA,EAACwH,EAAA,GAAEF,EAAgBE,EAAA,GAMD,MAAf/H,EAHJO,EAAI6G,EAAepH,EAASO,KAGa,MAAfP,EAAQO,GAAY,CAAC,IAADyH,EACjBN,EAAkB1H,EAASO,EAAE,oBAArDA,EAACyH,EAAA,GAAEF,EAAgBE,EAAA,EACxB,CACJ,MAAO,GAAuB,WAAnBJ,EAA6B,CACpC,IAAAK,EACyBP,EAAkB1H,EAASO,EAAG,oBAEvD,GAFCA,EAAC0H,EAAA,KAAEH,EAAgBG,EAAA,IAGhB,MAAM,IAAIvB,MAAM,0DAExB,CAEA,MAAO,CAACiB,aAAAA,EAAcE,iBAAAA,EAAkBC,iBAAAA,EAAkBrI,QAASc,EACvE,CAEA,SAASmH,EAAkB1H,EAASO,EAAG2H,GACnC,IAAIC,EAAgB,GACdpF,EAAY/C,EAAQO,GAC1B,GAAkB,MAAdwC,GAAmC,MAAdA,EACrB,MAAM,IAAI2D,MAAM,kCAAkC3D,EAAS,KAI/D,IAFAxC,IAEOA,EAAIP,EAAQT,QAAUS,EAAQO,KAAOwC,GACxCoF,GAAiBnI,EAAQO,GACzBA,IAGJ,GAAIP,EAAQO,KAAOwC,EACf,MAAM,IAAI2D,MAAM,gBAAgBwB,EAAI,UAGxC,MAAO,GADP3H,EACW4H,EACf,CAEA,SAASjB,EAAelH,EAASO,GAQ7BA,EAAI6G,EAAepH,EAASO,GAI5B,IADA,IAAI6H,EAAc,GACX7H,EAAIP,EAAQT,SAAW,KAAK+H,KAAKtH,EAAQO,KAC5C6H,GAAepI,EAAQO,GACvBA,IAIJ,IAAKgH,EAAmBa,GACpB,MAAM,IAAI1B,MAAM,0BAA0B0B,EAAW,KAKzD,IAAIC,EAAe,GAEnB,GAAkB,MAAfrI,EAHHO,EAAI6G,EAAepH,EAASO,KAGHsG,EAAO7G,EAAS,OAAOO,GAAIA,GAAG,OAClD,GAAkB,MAAfP,EAAQO,IAAcsG,EAAO7G,EAAS,KAAKO,GAAIA,GAAG,MACrD,IAAmB,MAAfP,EAAQO,GAab,MAAM,IAAImG,MAAM,sCAAsC1G,EAAQO,GAAE,KAThE,IAHAA,IAGOA,EAAIP,EAAQT,QAAyB,MAAfS,EAAQO,IACjC8H,GAAgBrI,EAAQO,GACxBA,IAEJ,GAAmB,MAAfP,EAAQO,GACR,MAAM,IAAImG,MAAM,6BAKxB,CAEA,MAAO,CACH0B,YAAAA,EACAC,aAAcA,EAAapH,OAC3BxB,MAAOc,EAEf,CAsHA,SAASsG,EAAOQ,EAAMiB,EAAI/H,GACtB,IAAI,IAAIgI,EAAE,EAAEA,EAAED,EAAI/I,OAAOgJ,IACrB,GAAGD,EAAIC,KAAKlB,EAAK9G,EAAEgI,EAAE,GAAI,OAAO,EAEpC,OAAO,CACX,CAEA,SAAShB,EAAmBiB,GACxB,GAAI7I,EAAO6I,GACd,OAAOA,EAEA,MAAM,IAAI9B,MAAM,uBAAuB8B,EAC/C,CChXA,MAAMC,EAAW,wBACXC,EAAW,qCAKXC,EAAW,CACblE,KAAO,EAEPC,cAAc,EACdkE,aAAc,IACdjE,WAAW,GAqEf,MAAMkE,EAAgB,0C,sGChFP,SAASC,EAAsB5E,GAC1C,MAAgC,mBAArBA,EACAA,EAEP6E,MAAM9D,QAAQf,GACP,SAACd,GACJ,QAAsC4F,EAAtCC,E,4rBAAAC,CAAsBhF,KAAgB8E,EAAAC,KAAAE,MAAE,CAAC,IAA9BC,EAAOJ,EAAAtK,MACd,GAAuB,iBAAZ0K,GAAwBhG,IAAagG,EAC5C,OAAO,EAEX,GAAIA,aAAmBvK,QAAUuK,EAAQ9B,KAAKlE,GAC1C,OAAO,CAEf,CACJ,EAEG,kBAAM,CAAK,CACtB,CCHA,IAEqBiG,EACnB,SAAYpJ,GACVvC,KAAKuC,QAAUA,EACfvC,KAAK4L,YAAc,KACnB5L,KAAK6L,cAAgB,GACrB7L,KAAK8L,gBAAkB,CAAC,EACxB9L,KAAK+L,aAAe,CAClB,KAAS,CAAEzK,MAAO,qBAAsB6F,IAAM,KAC9C,GAAO,CAAE7F,MAAO,mBAAoB6F,IAAM,KAC1C,GAAO,CAAE7F,MAAO,mBAAoB6F,IAAM,KAC1C,KAAS,CAAE7F,MAAO,qBAAsB6F,IAAM,MAEhDnH,KAAKgM,UAAY,CAAE1K,MAAO,oBAAqB6F,IAAM,KACrDnH,KAAK0H,aAAe,CAClB,MAAS,CAAEpG,MAAO,iBAAkB6F,IAAK,KAMzC,KAAS,CAAE7F,MAAO,iBAAkB6F,IAAK,KACzC,MAAU,CAAE7F,MAAO,kBAAmB6F,IAAK,KAC3C,IAAQ,CAAE7F,MAAO,gBAAiB6F,IAAK,KACvC,KAAS,CAAE7F,MAAO,kBAAmB6F,IAAK,KAC1C,UAAc,CAAE7F,MAAO,iBAAkB6F,IAAK,KAC9C,IAAQ,CAAE7F,MAAO,gBAAiB6F,IAAK,KACvC,IAAQ,CAAE7F,MAAO,iBAAkB6F,IAAK,KACxC,QAAW,CAAE7F,MAAO,mBAAoB6F,IAAM,SAAC8E,EAAGC,GAAG,OAAKC,OAAOC,cAAcC,OAAOC,SAASJ,EAAK,IAAI,GACxG,QAAW,CAAE5K,MAAO,0BAA2B6F,IAAM,SAAC8E,EAAGC,GAAG,OAAKC,OAAOC,cAAcC,OAAOC,SAASJ,EAAK,IAAI,IAEjHlM,KAAKuM,oBAAsBA,EAC3BvM,KAAKwM,SAAWA,EAChBxM,KAAKyM,cAAgBA,EACrBzM,KAAK0M,iBAAmBA,EACxB1M,KAAK2M,mBAAqBA,EAC1B3M,KAAK4M,aAAeA,EACpB5M,KAAK6M,qBAAuBA,EAC5B7M,KAAK8M,iBAAmBA,EACxB9M,KAAK+M,oBAAsBA,EAC3B/M,KAAKwI,SAAWA,EAChBxI,KAAKgN,mBAAqB5B,EAAsBpL,KAAKuC,QAAQiE,iBAC/D,EAIF,SAAS+F,EAAoBU,GAE3B,IADA,IAAMC,EAAU7M,OAAOuI,KAAKqE,GACnBpK,EAAI,EAAGA,EAAIqK,EAAQrL,OAAQgB,IAAK,CACvC,IAAMsK,EAAMD,EAAQrK,GACpB7C,KAAK+L,aAAaoB,GAAO,CACtB7L,MAAO,IAAIH,OAAO,IAAIgM,EAAI,IAAI,KAC9BhG,IAAM8F,EAAiBE,GAE5B,CACF,CAWA,SAASV,EAActF,EAAK7D,EAAS0E,EAAOoF,EAAUC,EAAeC,EAAYC,GAC/E,QAAY9H,IAAR0B,IACEnH,KAAKuC,QAAQqE,aAAewG,IAC9BjG,EAAMA,EAAI5D,QAET4D,EAAItF,OAAS,GAAE,CACZ0L,IAAgBpG,EAAMnH,KAAK6M,qBAAqB1F,IAEpD,IAAMqG,EAASxN,KAAKuC,QAAQ2E,kBAAkB5D,EAAS6D,EAAKa,EAAOqF,EAAeC,GAClF,OAAGE,QAEMrG,SACOqG,UAAkBrG,GAAOqG,IAAWrG,EAE3CqG,EACAxN,KAAKuC,QAAQqE,YAGDO,EAAI5D,SACL4D,EAHXsG,EAAWtG,EAAKnH,KAAKuC,QAAQmE,cAAe1G,KAAKuC,QAAQuE,oBAMvDK,CAGb,CAEJ,CAEA,SAASuF,EAAiBzH,GACxB,GAAIjF,KAAKuC,QAAQkE,eAAgB,CAC/B,IAAMhE,EAAOwC,EAAQiB,MAAM,KACrBwH,EAA+B,MAAtBzI,EAAQ0I,OAAO,GAAa,IAAM,GACjD,GAAgB,UAAZlL,EAAK,GACP,MAAO,GAEW,IAAhBA,EAAKZ,SACPoD,EAAUyI,EAASjL,EAAK,GAE5B,CACA,OAAOwC,CACT,CAIA,IAAM2I,EAAY,IAAIzM,OAAO,+CAAgD,MAE7E,SAASwL,EAAmBhJ,EAASqE,EAAO1E,GAC1C,IAAsC,IAAlCtD,KAAKuC,QAAQiE,kBAAgD,iBAAZ7C,EAAsB,CAOzE,IAHA,IAAMpC,EAAUH,EAAcuC,EAASiK,GACjC9L,EAAMP,EAAQM,OACdoG,EAAQ,CAAC,EACNpF,EAAI,EAAGA,EAAIf,EAAKe,IAAK,CAC5B,IAAM6C,EAAW1F,KAAK0M,iBAAiBnL,EAAQsB,GAAG,IAClD,IAAI7C,KAAKgN,mBAAmBtH,EAAUsC,GAAtC,CAGA,IAAI6F,EAAStM,EAAQsB,GAAG,GACpBiL,EAAQ9N,KAAKuC,QAAQ8D,oBAAsBX,EAC/C,GAAIA,EAAS7D,OAKX,GAJI7B,KAAKuC,QAAQuF,yBACfgG,EAAQ9N,KAAKuC,QAAQuF,uBAAuBgG,IAEjC,cAAVA,IAAuBA,EAAS,mBACpBrI,IAAXoI,EAAsB,CACpB7N,KAAKuC,QAAQqE,aACfiH,EAASA,EAAOtK,QAElBsK,EAAS7N,KAAK6M,qBAAqBgB,GACnC,IAAME,EAAS/N,KAAKuC,QAAQ6E,wBAAwB1B,EAAUmI,EAAQ7F,GAGpEC,EAAM6F,GAFLC,QAEcF,SACDE,UAAkBF,GAAUE,IAAWF,EAEtCE,EAGAN,EACbI,EACA7N,KAAKuC,QAAQoE,oBACb3G,KAAKuC,QAAQuE,mBAGnB,MAAW9G,KAAKuC,QAAQJ,yBACtB8F,EAAM6F,IAAS,EA7BnB,CAgCF,CACA,IAAKzN,OAAOuI,KAAKX,GAAOpG,OACtB,OAEF,GAAI7B,KAAKuC,QAAQ+D,oBAAqB,CACpC,IAAM0H,EAAiB,CAAC,EAExB,OADAA,EAAehO,KAAKuC,QAAQ+D,qBAAuB2B,EAC5C+F,CACT,CACA,OAAO/F,CACT,CACF,CAEA,IAAMuE,EAAW,SAASlK,GACxBA,EAAUA,EAAQwC,QAAQ,SAAU,MAKpC,IAJA,IAAMmJ,EAAS,IAAIC,EAAQ,QACvBtC,EAAcqC,EACdE,EAAW,GACXnG,EAAQ,GACJnF,EAAE,EAAGA,EAAGP,EAAQT,OAAQgB,IAE9B,GAAU,MADCP,EAAQO,GAIjB,GAAqB,MAAjBP,EAAQO,EAAE,GAAY,CACxB,IAAMuL,EAAaC,EAAiB/L,EAAS,IAAKO,EAAG,8BACjDS,EAAUhB,EAAQkB,UAAUX,EAAE,EAAEuL,GAAY7K,OAEhD,GAAGvD,KAAKuC,QAAQkE,eAAe,CAC7B,IAAM6H,EAAahL,EAAQiB,QAAQ,MAChB,IAAhB+J,IACDhL,EAAUA,EAAQV,OAAO0L,EAAW,GAExC,CAEGtO,KAAKuC,QAAQsF,mBACdvE,EAAUtD,KAAKuC,QAAQsF,iBAAiBvE,IAGvCsI,IACDuC,EAAWnO,KAAK+M,oBAAoBoB,EAAUvC,EAAa5D,IAI7D,IAAMuG,EAAcvG,EAAMxE,UAAUwE,EAAMwG,YAAY,KAAK,GAC3D,GAAGlL,IAA2D,IAAhDtD,KAAKuC,QAAQH,aAAamC,QAAQjB,GAC9C,MAAM,IAAI0F,MAAM,kDAAkD1F,EAAO,KAE3E,IAAImL,EAAY,EACbF,IAAmE,IAApDvO,KAAKuC,QAAQH,aAAamC,QAAQgK,IAClDE,EAAYzG,EAAMwG,YAAY,IAAKxG,EAAMwG,YAAY,KAAK,GAC1DxO,KAAK6L,cAAczH,OAEnBqK,EAAYzG,EAAMwG,YAAY,KAEhCxG,EAAQA,EAAMxE,UAAU,EAAGiL,GAE3B7C,EAAc5L,KAAK6L,cAAczH,MACjC+J,EAAW,GACXtL,EAAIuL,CACN,MAAO,GAAqB,MAAjB9L,EAAQO,EAAE,GAAY,CAE/B,IAAI6L,EAAUC,EAAWrM,EAAQO,GAAG,EAAO,MAC3C,IAAI6L,EAAS,MAAM,IAAI1F,MAAM,yBAG7B,GADAmF,EAAWnO,KAAK+M,oBAAoBoB,EAAUvC,EAAa5D,GACtDhI,KAAKuC,QAAQoF,mBAAyC,SAApB+G,EAAQpL,SAAuBtD,KAAKuC,QAAQqF,kBAE9E,CAEH,IAAMgH,EAAY,IAAIV,EAAQQ,EAAQpL,SACtCsL,EAAUtG,IAAItI,KAAKuC,QAAQgE,aAAc,IAEtCmI,EAAQpL,UAAYoL,EAAQG,QAAUH,EAAQI,iBAC/CF,EAAU,MAAQ5O,KAAK2M,mBAAmB+B,EAAQG,OAAQ7G,EAAO0G,EAAQpL,UAE3EtD,KAAKwI,SAASoD,EAAagD,EAAW5G,EAAOnF,EAC/C,CAGAA,EAAI6L,EAAQN,WAAa,CAC3B,MAAO,GAAgC,QAA7B9L,EAAQM,OAAOC,EAAI,EAAG,GAAc,CAC5C,IAAMkM,EAAWV,EAAiB/L,EAAS,SAAOO,EAAE,EAAG,0BACvD,GAAG7C,KAAKuC,QAAQiF,gBAAgB,CAAC,IAADwH,EACxB9F,EAAU5G,EAAQkB,UAAUX,EAAI,EAAGkM,EAAW,GAEpDZ,EAAWnO,KAAK+M,oBAAoBoB,EAAUvC,EAAa5D,GAE3D4D,EAAYtD,IAAItI,KAAKuC,QAAQiF,gBAAiB,EAAAwH,EAAA,GAAAA,EAAKhP,KAAKuC,QAAQgE,cAAgB2C,EAAO8F,IACzF,CACAnM,EAAIkM,CACN,MAAO,GAAiC,OAA7BzM,EAAQM,OAAOC,EAAI,EAAG,GAAa,CAC5C,IAAMY,EAASqF,EAAYxG,EAASO,GACpC7C,KAAK8L,gBAAkBrI,EAAOsF,SAC9BlG,EAAIY,EAAOZ,CACb,MAAM,GAAgC,OAA7BP,EAAQM,OAAOC,EAAI,EAAG,GAAa,CAC1C,IAAMuL,EAAaC,EAAiB/L,EAAS,MAAOO,EAAG,wBAA0B,EAC3EgM,EAASvM,EAAQkB,UAAUX,EAAI,EAAEuL,GAEvCD,EAAWnO,KAAK+M,oBAAoBoB,EAAUvC,EAAa5D,GAE3D,IAI8BiH,EAJ1B9H,EAAMnH,KAAKyM,cAAcoC,EAAQjD,EAAY3G,QAAS+C,GAAO,GAAM,GAAO,GAAM,GAC1EvC,MAAP0B,IAAkBA,EAAM,IAGxBnH,KAAKuC,QAAQsE,cACd+E,EAAYtD,IAAItI,KAAKuC,QAAQsE,cAAe,EAAAoI,EAAA,GAAAA,EAAKjP,KAAKuC,QAAQgE,cAAgBsI,EAAMI,KAEpFrD,EAAYtD,IAAItI,KAAKuC,QAAQgE,aAAcY,GAG7CtE,EAAIuL,EAAa,CACnB,KAAM,CACJ,IAAI3K,EAASkL,EAAWrM,EAAQO,EAAG7C,KAAKuC,QAAQkE,gBAC5CnD,EAASG,EAAOH,QACd4L,EAAazL,EAAOyL,WACtBL,EAASpL,EAAOoL,OAChBC,EAAiBrL,EAAOqL,eACxBV,EAAa3K,EAAO2K,WAEpBpO,KAAKuC,QAAQsF,mBACfvE,EAAUtD,KAAKuC,QAAQsF,iBAAiBvE,IAItCsI,GAAeuC,GACU,SAAxBvC,EAAY3G,UAEbkJ,EAAWnO,KAAK+M,oBAAoBoB,EAAUvC,EAAa5D,GAAO,IAKtE,IAAMmH,EAAUvD,EACbuD,IAAmE,IAAxDnP,KAAKuC,QAAQH,aAAamC,QAAQ4K,EAAQlK,WACtD2G,EAAc5L,KAAK6L,cAAczH,MACjC4D,EAAQA,EAAMxE,UAAU,EAAGwE,EAAMwG,YAAY,OAE5ClL,IAAY2K,EAAOhJ,UACpB+C,GAASA,EAAQ,IAAM1E,EAAUA,GAEnC,IAAM3B,EAAakB,EACnB,GAAI7C,KAAK4M,aAAa5M,KAAKuC,QAAQ8E,UAAWW,EAAO1E,GAAU,CAC7D,IAAI8L,EAAa,GAEjB,GAAGP,EAAOhN,OAAS,GAAKgN,EAAOL,YAAY,OAASK,EAAOhN,OAAS,EAC/B,MAAhCyB,EAAQA,EAAQzB,OAAS,IAC1ByB,EAAUA,EAAQV,OAAO,EAAGU,EAAQzB,OAAS,GAC7CmG,EAAQA,EAAMpF,OAAO,EAAGoF,EAAMnG,OAAS,GACvCgN,EAASvL,GAETuL,EAASA,EAAOjM,OAAO,EAAGiM,EAAOhN,OAAS,GAE5CgB,EAAIY,EAAO2K,gBAGR,IAAmD,IAAhDpO,KAAKuC,QAAQH,aAAamC,QAAQjB,GAExCT,EAAIY,EAAO2K,eAGT,CAEF,IAAM3K,EAASzD,KAAK8M,iBAAiBxK,EAAS4M,EAAYd,EAAa,GACvE,IAAI3K,EAAQ,MAAM,IAAIuF,MAAM,qBAAqBkG,GACjDrM,EAAIY,EAAOZ,EACXuM,EAAa3L,EAAO2L,UACtB,CAEA,IAAMR,EAAY,IAAIV,EAAQ5K,GAE3BA,IAAYuL,GAAUC,IACvBF,EAAU,MAAQ5O,KAAK2M,mBAAmBkC,EAAQ7G,EAAO1E,IAExD8L,IACDA,EAAapP,KAAKyM,cAAc2C,EAAY9L,EAAS0E,GAAO,EAAM8G,GAAgB,GAAM,IAG1F9G,EAAQA,EAAMpF,OAAO,EAAGoF,EAAMwG,YAAY,MAC1CI,EAAUtG,IAAItI,KAAKuC,QAAQgE,aAAc6I,GAEzCpP,KAAKwI,SAASoD,EAAagD,EAAW5G,EAAOrG,EAC/C,KAAK,CAEH,GAAGkN,EAAOhN,OAAS,GAAKgN,EAAOL,YAAY,OAASK,EAAOhN,OAAS,EAAE,CACjC,MAAhCyB,EAAQA,EAAQzB,OAAS,IAC1ByB,EAAUA,EAAQV,OAAO,EAAGU,EAAQzB,OAAS,GAC7CmG,EAAQA,EAAMpF,OAAO,EAAGoF,EAAMnG,OAAS,GACvCgN,EAASvL,GAETuL,EAASA,EAAOjM,OAAO,EAAGiM,EAAOhN,OAAS,GAGzC7B,KAAKuC,QAAQsF,mBACdvE,EAAUtD,KAAKuC,QAAQsF,iBAAiBvE,IAG1C,IAAMsL,EAAY,IAAIV,EAAQ5K,GAC3BA,IAAYuL,GAAUC,IACvBF,EAAU,MAAQ5O,KAAK2M,mBAAmBkC,EAAQ7G,EAAO1E,IAE3DtD,KAAKwI,SAASoD,EAAagD,EAAW5G,EAAOrG,GAC7CqG,EAAQA,EAAMpF,OAAO,EAAGoF,EAAMwG,YAAY,KAC5C,KAEI,CACF,IAAMI,EAAY,IAAIV,EAAS5K,GAC/BtD,KAAK6L,cAAc7J,KAAK4J,GAErBtI,IAAYuL,GAAUC,IACvBF,EAAU,MAAQ5O,KAAK2M,mBAAmBkC,EAAQ7G,EAAO1E,IAE3DtD,KAAKwI,SAASoD,EAAagD,EAAW5G,EAAOrG,GAC7CiK,EAAcgD,CAChB,CACAT,EAAW,GACXtL,EAAIuL,CACN,CACF,MAEAD,GAAY7L,EAAQO,GAGxB,OAAOoL,EAAO7F,KAChB,EAEA,SAASI,EAASoD,EAAagD,EAAW5G,EAAOrG,GAE1C3B,KAAKuC,QAAQ2F,kBAAiBvG,OAAa8D,GAChD,IAAMhC,EAASzD,KAAKuC,QAAQwF,UAAU6G,EAAU3J,QAAS+C,EAAO4G,EAAU,QAC5D,IAAXnL,IACyB,iBAAXA,GACfmL,EAAU3J,QAAUxB,EACpBmI,EAAYpD,SAASoG,EAAWjN,IAEhCiK,EAAYpD,SAASoG,EAAWjN,GAEpC,CAEA,IAAMkL,EAAuB,SAAS1F,GAEpC,GAAGnH,KAAKuC,QAAQkF,gBAAgB,CAC9B,IAAI,IAAI2B,KAAcpJ,KAAK8L,gBAAgB,CACzC,IAAMuD,EAASrP,KAAK8L,gBAAgB1C,GACpCjC,EAAMA,EAAIrC,QAASuK,EAAO9F,KAAM8F,EAAOlI,IACzC,CACA,IAAI,IAAIiC,KAAcpJ,KAAK+L,aAAa,CACtC,IAAMsD,EAASrP,KAAK+L,aAAa3C,GACjCjC,EAAMA,EAAIrC,QAASuK,EAAO/N,MAAO+N,EAAOlI,IAC1C,CACA,GAAGnH,KAAKuC,QAAQmF,aACd,IAAI,IAAI0B,KAAcpJ,KAAK0H,aAAa,CACtC,IAAM2H,EAASrP,KAAK0H,aAAa0B,GACjCjC,EAAMA,EAAIrC,QAASuK,EAAO/N,MAAO+N,EAAOlI,IAC1C,CAEFA,EAAMA,EAAIrC,QAAS9E,KAAKgM,UAAU1K,MAAOtB,KAAKgM,UAAU7E,IAC1D,CACA,OAAOA,CACT,EACA,SAAS4F,EAAoBoB,EAAUvC,EAAa5D,EAAOsF,GAezD,OAdIa,SACgB1I,IAAf6H,IAA0BA,EAA0C,IAA7B1B,EAAYxD,MAAMvG,aAS3C4D,KAPjB0I,EAAWnO,KAAKyM,cAAc0B,EAC5BvC,EAAY3G,QACZ+C,GACA,IACA4D,EAAY,OAAkD,IAA1CvL,OAAOuI,KAAKgD,EAAY,OAAO/J,OACnDyL,KAEyC,KAAba,GAC5BvC,EAAYtD,IAAItI,KAAKuC,QAAQgE,aAAc4H,GAC7CA,EAAW,IAENA,CACT,CASA,SAASvB,EAAavF,EAAWW,EAAOsH,GACtC,IAAMC,EAAc,KAAOD,EAC3B,IAAK,IAAME,KAAgBnI,EAAW,CACpC,IAAMoI,EAAcpI,EAAUmI,GAC9B,GAAID,IAAgBE,GAAezH,IAAUyH,EAAe,OAAO,CACrE,CACA,OAAO,CACT,CAsCA,SAASpB,EAAiB/L,EAAS4J,EAAKrJ,EAAG6M,GACzC,IAAMC,EAAerN,EAAQiC,QAAQ2H,EAAKrJ,GAC1C,IAAqB,IAAlB8M,EACD,MAAM,IAAI3G,MAAM0G,GAEhB,OAAOC,EAAezD,EAAIrK,OAAS,CAEvC,CAEA,SAAS8M,EAAWrM,EAAQO,EAAG4D,EAAgBmJ,QAAW,IAAXA,IAAAA,EAAc,KAC3D,IAAMnM,EAxCR,SAAgCnB,EAASO,EAAG+M,GAC1C,IAAIC,OADiD,IAAXD,IAAAA,EAAc,KAGxD,IADA,IAAIf,EAAS,GACJ9M,EAAQc,EAAGd,EAAQO,EAAQT,OAAQE,IAAS,CACnD,IAAI+N,EAAKxN,EAAQP,GACjB,GAAI8N,EACIC,IAAOD,IAAcA,EAAe,SACrC,GAAW,MAAPC,GAAqB,MAAPA,EACrBD,EAAeC,OACZ,GAAIA,IAAOF,EAAY,GAAI,CAChC,IAAGA,EAAY,GAQb,MAAO,CACLjG,KAAMkF,EACN9M,MAAOA,GATT,GAAGO,EAAQP,EAAQ,KAAO6N,EAAY,GACpC,MAAO,CACLjG,KAAMkF,EACN9M,MAAOA,EASf,KAAkB,OAAP+N,IACTA,EAAK,KAEPjB,GAAUiB,CACZ,CACF,CAYiBC,CAAuBzN,EAASO,EAAE,EAAG+M,GACpD,GAAInM,EAAJ,CACA,IAAIoL,EAASpL,EAAOkG,KACdyE,EAAa3K,EAAO1B,MACpBiO,EAAiBnB,EAAOoB,OAAO,MACjC3M,EAAUuL,EACVC,GAAiB,GACE,IAApBkB,IACD1M,EAAUuL,EAAOrL,UAAU,EAAGwM,GAC9BnB,EAASA,EAAOrL,UAAUwM,EAAiB,GAAGE,aAGhD,IAAMhB,EAAa5L,EACnB,GAAGmD,EAAe,CAChB,IAAM6H,EAAahL,EAAQiB,QAAQ,MAChB,IAAhB+J,IAEDQ,GADAxL,EAAUA,EAAQV,OAAO0L,EAAW,MACP7K,EAAOkG,KAAK/G,OAAO0L,EAAa,GAEjE,CAEA,MAAO,CACLhL,QAASA,EACTuL,OAAQA,EACRT,WAAYA,EACZU,eAAgBA,EAChBI,WAAYA,EAzBI,CA2BpB,CAOA,SAASpC,EAAiBxK,EAASgB,EAAST,GAK1C,IAJA,IAAMlB,EAAakB,EAEfsN,EAAe,EAEZtN,EAAIP,EAAQT,OAAQgB,IACzB,GAAmB,MAAfP,EAAQO,GACV,GAAqB,MAAjBP,EAAQO,EAAE,GAAY,CACtB,IAAMuL,EAAaC,EAAiB/L,EAAS,IAAKO,EAAMS,EAAO,kBAE/D,GADmBhB,EAAQkB,UAAUX,EAAE,EAAEuL,GAAY7K,SACjCD,GAEG,KADrB6M,EAEE,MAAO,CACLf,WAAY9M,EAAQkB,UAAU7B,EAAYkB,GAC1CA,EAAIuL,GAIVvL,EAAEuL,CACJ,MAAO,GAAoB,MAAjB9L,EAAQO,EAAE,GAElBA,EADmBwL,EAAiB/L,EAAS,KAAMO,EAAE,EAAG,gCAEnD,GAAgC,QAA7BP,EAAQM,OAAOC,EAAI,EAAG,GAE9BA,EADmBwL,EAAiB/L,EAAS,SAAOO,EAAE,EAAG,gCAEpD,GAAgC,OAA7BP,EAAQM,OAAOC,EAAI,EAAG,GAE9BA,EADmBwL,EAAiB/L,EAAS,MAAOO,EAAG,2BAA6B,MAE/E,CACL,IAAM6L,EAAUC,EAAWrM,EAASO,EAAG,KAEnC6L,KACkBA,GAAWA,EAAQpL,WACnBA,GAAuD,MAA5CoL,EAAQG,OAAOH,EAAQG,OAAOhN,OAAO,IAClEsO,IAEFtN,EAAE6L,EAAQN,WAEd,CAGR,CAEA,SAASX,EAAWtG,EAAKiJ,EAAa7N,GACpC,GAAI6N,GAA8B,iBAARjJ,EAAkB,CAE1C,IAAMqG,EAASrG,EAAI5D,OACnB,MAAc,SAAXiK,GACgB,UAAXA,GFrkBG,SAAkBtB,EAAK3J,EAAU,CAAC,GAE7C,GADAA,EAAUlC,OAAOmC,OAAO,CAAC,EAAGyI,EAAU1I,IAClC2J,GAAsB,iBAARA,EAAmB,OAAOA,EAE5C,IAAImE,EAAcnE,EAAI3I,OAEtB,QAAwBkC,IAArBlD,EAAQ+N,UAA0B/N,EAAQ+N,SAAS1G,KAAKyG,GAAa,OAAOnE,EAC1E,GAAS,MAANA,EAAW,OAAO,EACrB,GAAI3J,EAAQwE,KAAOgE,EAASnB,KAAKyG,GAClC,OAkGR,SAAmBE,GAEf,GAAGjE,SAAU,OAAOA,SAASiE,EApGI,IAqG5B,GAAGlE,OAAOC,SAAU,OAAOD,OAAOC,SAASiE,EArGf,IAsG5B,GAAGC,QAAUA,OAAOlE,SAAU,OAAOkE,OAAOlE,SAASiE,EAtGzB,IAuG5B,MAAM,IAAIvH,MAAM,+DACzB,CAxGeyH,CAAUJ,GAGf,IAAsC,IAAlCA,EAAWJ,OAAO,YACxB,OAqDR,SAA0B/D,EAAImE,EAAW9N,GACrC,IAAIA,EAAQ0E,UAAW,OAAOiF,EAC9B,MAAMwE,EAAWL,EAAW7O,MAAM2J,GAClC,GAAGuF,EAAS,CACR,IAAIC,EAAOD,EAAS,IAAM,GAC1B,MAAME,GAAsC,IAA9BF,EAAS,GAAGnM,QAAQ,KAAc,IAAM,IAChDyC,EAAe0J,EAAS,GACxBG,EAA0BF,EAC5BzE,EAAIlF,EAAanF,OAAO,KAAO+O,EAC7B1E,EAAIlF,EAAanF,UAAY+O,EAEnC,OAAG5J,EAAanF,OAAS,GAAKgP,EAAgC3E,EAC9B,IAAxBlF,EAAanF,SACb6O,EAAS,GAAGI,WAAW,IAAIF,MAAYF,EAAS,GAAG,KAAOE,EAEzDrO,EAAQyE,eAAiB6J,GAE9BR,GAAcK,EAAS,IAAM,IAAMA,EAAS,GACrCrE,OAAOgE,IACLnE,EALEG,OAAOgE,EAM1B,CACI,OAAOnE,CAEf,CA5Ee6E,CAAiB7E,EAAImE,EAAW9N,GAGtC,CAED,MAAMf,EAAQwJ,EAASvJ,KAAK4O,GAE5B,GAAG7O,EAAM,CACL,MAAMmP,EAAOnP,EAAM,IAAM,GACnBwF,EAAexF,EAAM,GAC3B,IAAIwP,GAyEGT,EAzE2B/O,EAAM,MA0EV,IAAzB+O,EAAOhM,QAAQ,MAEV,OADdgM,EAASA,EAAOzL,QAAQ,MAAO,KACXyL,EAAS,IACP,MAAdA,EAAO,GAAaA,EAAS,IAAIA,EACL,MAA5BA,EAAOA,EAAO1O,OAAO,KAAa0O,EAASA,EAAO/M,UAAU,EAAE+M,EAAO1O,OAAO,IAC7E0O,GAEJA,EAhFC,MAAMU,EAAgCN,EACH,MAA/BzE,EAAIlF,EAAanF,OAAO,GACO,MAA7BqK,EAAIlF,EAAanF,QAGvB,IAAIU,EAAQyE,eACJA,EAAanF,OAAS,GACM,IAAxBmF,EAAanF,SAAiBoP,GAEtC,OAAO/E,EAEP,CACA,MAAMgF,EAAM7E,OAAOgE,GACbc,EAAYhF,OAAO+E,GAEzB,GAAY,IAARA,IAAsB,IAATA,EAAY,OAAOA,EACpC,IAAiC,IAA9BC,EAAUlB,OAAO,QAChB,OAAG1N,EAAQ0E,UAAkBiK,EACjBhF,EACV,IAAgC,IAA7BmE,EAAW9L,QAAQ,KACxB,MAAiB,MAAd4M,GACKA,IAAcH,GACbG,IAAc,GAAGR,IAAOK,IAFJE,EAGjBhF,EAGhB,IAAIkF,EAAIpK,EAAcgK,EAAoBX,EAC1C,OAAGrJ,EAESoK,IAAMD,GAAeR,EAAKS,IAAMD,EAAaD,EAAMhF,EAGnDkF,IAAMD,GAAeC,IAAMT,EAAKQ,EAAaD,EAAMhF,CAEnE,CACJ,CACI,OAAOA,CAEf,CAkCJ,IAAmBqE,CAjCnB,CEugBgBc,CAASlK,EAAK5E,EAC5B,CACE,YP1jBkB,IO0jBN4E,EACHA,EAEA,EAGb,CCzlBA,IAAMhB,EAAkBgC,EAAQU,oBAQjB,SAASyI,GAAS7I,EAAMlG,GACrC,OAAOgP,GAAU9I,EAAMlG,EACzB,CASA,SAASgP,GAASC,EAAKjP,EAASyF,GAG9B,IAFA,IAAIyJ,EACEC,EAAgB,CAAC,EACd7O,EAAI,EAAGA,EAAI2O,EAAI3P,OAAQgB,IAAK,CACnC,IAEI8O,EAFEC,EAASJ,EAAI3O,GACbgP,EAAWC,GAASF,GAK1B,GAHwBD,OAAXlM,IAAVuC,EAAgC6J,EACnB7J,EAAQ,IAAM6J,EAE3BA,IAAatP,EAAQgE,kBACVd,IAATgM,EAAoBA,EAAOG,EAAOC,GAChCJ,GAAQ,GAAKG,EAAOC,OACrB,SAAgBpM,IAAboM,EACP,SACI,GAAGD,EAAOC,GAAU,CAExB,IAAI1K,EAAMoK,GAASK,EAAOC,GAAWtP,EAASoP,GACxCI,EAASC,GAAU7K,EAAK5E,QACEkD,IAA5BmM,EAAOzL,KACTgB,EAAIhB,GAAmByL,EAAOzL,IAG7ByL,EAAO,MACRK,GAAkB9K,EAAKyK,EAAO,MAAOD,EAAUpP,GACZ,IAA5BlC,OAAOuI,KAAKzB,GAAKtF,aAA8C4D,IAA9B0B,EAAI5E,EAAQgE,eAAgChE,EAAQ+E,qBAEzD,IAA5BjH,OAAOuI,KAAKzB,GAAKtF,SACrBU,EAAQ+E,qBAAsBH,EAAI5E,EAAQgE,cAAgB,GACxDY,EAAM,IAHXA,EAAMA,EAAI5E,EAAQgE,mBAMWd,IAA5BiM,EAAcG,IAA2BH,EAAc9Q,eAAeiR,IACnExG,MAAM9D,QAAQmK,EAAcG,MAC5BH,EAAcG,GAAY,CAAEH,EAAcG,KAE9CH,EAAcG,GAAU7P,KAAKmF,IAIzB5E,EAAQgF,QAAQsK,EAAUF,EAAUI,GACtCL,EAAcG,GAAY,CAAC1K,GAE3BuK,EAAcG,GAAY1K,CAGhC,EAEF,CAKA,MAHmB,iBAATsK,EACLA,EAAK5P,OAAS,IAAG6P,EAAcnP,EAAQgE,cAAgBkL,QAC1ChM,IAATgM,IAAoBC,EAAcnP,EAAQgE,cAAgBkL,GAC5DC,CACT,CAEA,SAASI,GAASrR,GAEhB,IADA,IAAMmI,EAAOvI,OAAOuI,KAAKnI,GAChBoC,EAAI,EAAGA,EAAI+F,EAAK/G,OAAQgB,IAAK,CACpC,IAAM1C,EAAMyI,EAAK/F,GACjB,GAAW,OAAR1C,EAAc,OAAOA,CAC1B,CACF,CAEA,SAAS8R,GAAiBxR,EAAKyR,EAASC,EAAO5P,GAC7C,GAAI2P,EAGF,IAFA,IAAMtJ,EAAOvI,OAAOuI,KAAKsJ,GACnBpQ,EAAM8G,EAAK/G,OACRgB,EAAI,EAAGA,EAAIf,EAAKe,IAAK,CAC5B,IAAMuP,EAAWxJ,EAAK/F,GAClBN,EAAQgF,QAAQ6K,EAAUD,EAAQ,IAAMC,GAAU,GAAM,GAC1D3R,EAAI2R,GAAY,CAAEF,EAAQE,IAE1B3R,EAAI2R,GAAYF,EAAQE,EAE5B,CAEJ,CAEA,SAASJ,GAAUvR,EAAK8B,GACtB,IAAQgE,EAAiBhE,EAAjBgE,aACF8L,EAAYhS,OAAOuI,KAAKnI,GAAKoB,OAEnC,OAAkB,IAAdwQ,KAKY,IAAdA,IACC5R,EAAI8F,IAA8C,kBAAtB9F,EAAI8F,IAAqD,IAAtB9F,EAAI8F,GAMxE,CClHmC,IAEd+L,GAAS,WAE1B,SAAAA,EAAY/P,GACRvC,KAAKiN,iBAAmB,CAAC,EACzBjN,KAAKuC,QPiCe,SAASA,GACjC,OAAOlC,OAAOmC,OAAO,CAAC,EAAGN,EAAgBK,EAC7C,COnCuBgQ,CAAahQ,EAEhC,CACA,IAAA8F,EAAAiK,EAAA3R,UAwDC,OAxDD0H,EAKAmK,MAAA,SAAMlQ,EAAQmQ,GACV,GAAsB,iBAAZnQ,OACJ,KAAIA,EAAQoQ,SAGd,MAAM,IAAI1J,MAAM,mDAFhB1G,EAAUA,EAAQoQ,UAGtB,CACA,GAAID,EAAiB,EACO,IAArBA,IAA2BA,EAAmB,CAAC,GAElD,IAAMhP,EAASpB,EAASC,EAASmQ,GACjC,IAAe,IAAXhP,EACF,MAAMuF,MAAUvF,EAAOV,IAAIiB,IAAG,IAAIP,EAAOV,IAAIkB,KAAI,IAAIR,EAAOV,IAAIuB,IAEpE,CACF,IAAMqO,EAAmB,IAAIhH,EAAiB3L,KAAKuC,SACnDoQ,EAAiBpG,oBAAoBvM,KAAKiN,kBAC1C,IAAM2F,EAAgBD,EAAiBnG,SAASlK,GAChD,OAAGtC,KAAKuC,QAAQ6D,oBAAmCX,IAAlBmN,EAAoCA,EACzDtB,GAASsB,EAAe5S,KAAKuC,QAC7C,EAEA8F,EAKAwK,UAAA,SAAU1S,EAAKa,GACX,IAA2B,IAAxBA,EAAMuD,QAAQ,KACb,MAAM,IAAIyE,MAAM,+BACd,IAAyB,IAAtB7I,EAAIoE,QAAQ,OAAqC,IAAtBpE,EAAIoE,QAAQ,KAC5C,MAAM,IAAIyE,MAAM,wEACd,GAAa,MAAVhI,EACL,MAAM,IAAIgI,MAAM,6CAEhBhJ,KAAKiN,iBAAiB9M,GAAOa,CAErC,EAEAsR,EAUOzJ,kBAAP,WACI,OAAOV,EAAQU,mBACnB,EAACyJ,CAAA,CA/DyB,GCEf,SAASQ,GAAMC,EAAQxQ,GAClC,IAAIyQ,EAAc,GAIlB,OAHIzQ,EAAQ0Q,QAAU1Q,EAAQ2Q,SAASrR,OAAS,IAC5CmR,EAXI,MAaDG,GAASJ,EAAQxQ,EAAS,GAAIyQ,EACzC,CAEA,SAASG,GAAS3B,EAAKjP,EAASyF,EAAOgL,GAInC,IAHA,IAAII,EAAS,GACTC,GAAuB,EAElBxQ,EAAI,EAAGA,EAAI2O,EAAI3P,OAAQgB,IAAK,CACjC,IAAM+O,EAASJ,EAAI3O,GACbS,EAAUwO,GAASF,GACzB,QAAenM,IAAZnC,EAAH,CAEA,IAAIgQ,EAIJ,GAHwBA,EAAH,IAAjBtL,EAAMnG,OAAyByB,EAChB0E,EAAK,IAAI1E,EAExBA,IAAYf,EAAQgE,aAYjB,GAAIjD,IAAYf,EAAQsE,cAOxB,GAAIvD,IAAYf,EAAQiF,gBAIxB,GAAmB,MAAflE,EAAQ,GAAZ,CASP,IAAIiQ,EAAgBP,EACE,KAAlBO,IACAA,GAAiBhR,EAAQ2Q,UAE7B,IACMM,EAAWR,EAAW,IAAO1P,EADpBmQ,GAAY7B,EAAO,MAAOrP,GAEnCmR,EAAWP,GAASvB,EAAOtO,GAAUf,EAAS+Q,EAAUC,IACf,IAA3ChR,EAAQH,aAAamC,QAAQjB,GACzBf,EAAQoR,qBAAsBP,GAAUI,EAAW,IAClDJ,GAAUI,EAAW,KACjBE,GAAgC,IAApBA,EAAS7R,SAAiBU,EAAQqR,kBAEhDF,GAAYA,EAASG,SAAS,KACrCT,GAAUI,EAAQ,IAAOE,EAAWV,EAAW,KAAK1P,EAAO,KAE3D8P,GAAUI,EAAW,IACjBE,GAA4B,KAAhBV,IAAuBU,EAASI,SAAS,OAASJ,EAASI,SAAS,OAChFV,GAAUJ,EAAczQ,EAAQ2Q,SAAWQ,EAAWV,EAEtDI,GAAUM,EAEdN,GAAM,KAAS9P,EAAO,KAVtB8P,GAAUI,EAAW,KAYzBH,GAAuB,CAxBvB,KARO,CACH,IAAMU,EAASN,GAAY7B,EAAO,MAAOrP,GACnCyR,EAAsB,SAAZ1Q,EAAqB,GAAK0P,EACtCiB,EAAiBrC,EAAOtO,GAAS,GAAGf,EAAQgE,cAEhD6M,GAAUY,EAAO,IAAO1Q,GADxB2Q,EAA2C,IAA1BA,EAAepS,OAAe,IAAMoS,EAAiB,IACnBF,EAAM,KACzDV,GAAuB,CAE3B,MAXID,GAAUJ,EAAW,UAAUpB,EAAOtO,GAAS,GAAGf,EAAQgE,cAAa,SACvE8M,GAAuB,OARnBA,IACAD,GAAUJ,GAEdI,GAAM,YAAgBxB,EAAOtO,GAAS,GAAGf,EAAQgE,cAAa,MAC9D8M,GAAuB,MAjB3B,CACI,IAAIa,EAAUtC,EAAOtO,GAChB6Q,GAAWb,EAAU/Q,KAEtB2R,EAAUrH,GADVqH,EAAU3R,EAAQ2E,kBAAkB5D,EAAS4Q,GACL3R,IAExC8Q,IACAD,GAAUJ,GAEdI,GAAUc,EACVb,GAAuB,CAqB3B,CArCkC,CA8DtC,CAEA,OAAOD,CACX,CAEA,SAAStB,GAASrR,GAEd,IADA,IAAMmI,EAAOvI,OAAOuI,KAAKnI,GAChBoC,EAAI,EAAGA,EAAI+F,EAAK/G,OAAQgB,IAAK,CAClC,IAAM1C,EAAMyI,EAAK/F,GACjB,GAAIpC,EAAIG,eAAeT,IACX,OAARA,EAAc,OAAOA,CAC7B,CACJ,CAEA,SAASsT,GAAYvB,EAAS3P,GAC1B,IAAIoB,EAAU,GACd,GAAIuO,IAAY3P,EAAQiE,iBACpB,IAAK,IAAI4N,KAAQlC,EACb,GAAIA,EAAQtR,eAAewT,GAA3B,CACA,IAAIC,EAAU9R,EAAQ6E,wBAAwBgN,EAAMlC,EAAQkC,KAE5C,KADhBC,EAAUxH,GAAqBwH,EAAS9R,KAChBA,EAAQ+R,0BAC5B3Q,GAAO,IAAQyQ,EAAKxR,OAAOL,EAAQ8D,oBAAoBxE,QAEvD8B,GAAO,IAAQyQ,EAAKxR,OAAOL,EAAQ8D,oBAAoBxE,QAAO,KAAKwS,EAAO,GANpC,CAUlD,OAAO1Q,CACX,CAEA,SAASwQ,GAAWnM,EAAOzF,GAEvB,IAAIe,GADJ0E,EAAQA,EAAMpF,OAAO,EAAGoF,EAAMnG,OAASU,EAAQgE,aAAa1E,OAAS,IACjDe,OAAOoF,EAAMwG,YAAY,KAAO,GACpD,IAAK,IAAIzM,KAASQ,EAAQ8E,UACtB,GAAI9E,EAAQ8E,UAAUtF,KAAWiG,GAASzF,EAAQ8E,UAAUtF,KAAW,KAAOuB,EAAS,OAAO,EAElG,OAAO,CACX,CAEA,SAASuJ,GAAqB0H,EAAWhS,GACrC,GAAIgS,GAAaA,EAAU1S,OAAS,GAAKU,EAAQkF,gBAC7C,IAAK,IAAI5E,EAAI,EAAGA,EAAIN,EAAQwG,SAASlH,OAAQgB,IAAK,CAC9C,IAAMwM,EAAS9M,EAAQwG,SAASlG,GAChC0R,EAAYA,EAAUzP,QAAQuK,EAAO/N,MAAO+N,EAAOlI,IACvD,CAEJ,OAAOoN,CACX,CChIA,IAAMrS,GAAiB,CACrBmE,oBAAqB,KACrBC,qBAAqB,EACrBC,aAAc,QACdC,kBAAkB,EAClBK,eAAe,EACfoM,QAAQ,EACRC,SAAU,KACVU,mBAAmB,EACnBD,sBAAsB,EACtBW,2BAA2B,EAC3BpN,kBAAmB,SAAS/G,EAAKqU,GAC/B,OAAOA,CACT,EACApN,wBAAyB,SAAS1B,EAAU8O,GAC1C,OAAOA,CACT,EACApO,eAAe,EACfoB,iBAAiB,EACjBpF,aAAc,GACd2G,SAAU,CACR,CAAEzH,MAAO,IAAIH,OAAO,IAAK,KAAMgG,IAAK,SACpC,CAAE7F,MAAO,IAAIH,OAAO,IAAK,KAAMgG,IAAK,QACpC,CAAE7F,MAAO,IAAIH,OAAO,IAAK,KAAMgG,IAAK,QACpC,CAAE7F,MAAO,IAAIH,OAAO,IAAM,KAAMgG,IAAK,UACrC,CAAE7F,MAAO,IAAIH,OAAO,IAAM,KAAMgG,IAAK,WAEvCM,iBAAiB,EACjBJ,UAAW,GAGXoN,cAAc,GAGD,SAASC,GAAQnS,GAC9BvC,KAAKuC,QAAUlC,OAAOmC,OAAO,CAAC,EAAGN,GAAgBK,IACX,IAAlCvC,KAAKuC,QAAQiE,kBAA6BxG,KAAKuC,QAAQ+D,oBACzDtG,KAAK2U,YAAc,WACjB,OAAO,CACT,GAEA3U,KAAKgN,mBAAqB5B,EAAsBpL,KAAKuC,QAAQiE,kBAC7DxG,KAAK4U,cAAgB5U,KAAKuC,QAAQ8D,oBAAoBxE,OACtD7B,KAAK2U,YAAcA,IAGrB3U,KAAK6U,qBAAuBA,GAExB7U,KAAKuC,QAAQ0Q,QACfjT,KAAK8U,UAAYA,GACjB9U,KAAK+U,WAAa,MAClB/U,KAAKgV,QAAU,OAEfhV,KAAK8U,UAAY,WACf,MAAO,EACT,EACA9U,KAAK+U,WAAa,IAClB/U,KAAKgV,QAAU,GAEnB,CAmHA,SAASH,GAAsBI,EAAQ9U,EAAK+U,EAAOC,GACjD,IAAM1R,EAASzD,KAAKoV,IAAIH,EAAQC,EAAQ,EAAGC,EAAOE,OAAOlV,IACzD,YAA0CsF,IAAtCwP,EAAOjV,KAAKuC,QAAQgE,eAA8D,IAA/BlG,OAAOuI,KAAKqM,GAAQpT,OAClE7B,KAAKsV,iBAAiBL,EAAOjV,KAAKuC,QAAQgE,cAAepG,EAAKsD,EAAOE,QAASuR,GAE9ElV,KAAKuV,gBAAgB9R,EAAO0D,IAAKhH,EAAKsD,EAAOE,QAASuR,EAEjE,CAuFA,SAASJ,GAAUI,GACjB,OAAOlV,KAAKuC,QAAQ2Q,SAASsC,OAAON,EACtC,CAEA,SAASP,GAAY7J,GACnB,SAAIA,EAAKgG,WAAW9Q,KAAKuC,QAAQ8D,sBAAwByE,IAAS9K,KAAKuC,QAAQgE,eACtEuE,EAAKlI,OAAO5C,KAAK4U,cAI5B,CAzNAF,GAAQ/T,UAAU8U,MAAQ,SAASC,GACjC,OAAG1V,KAAKuC,QAAQ6D,cACPuP,GAAmBD,EAAM1V,KAAKuC,UAElC8I,MAAM9D,QAAQmO,IAAS1V,KAAKuC,QAAQqT,eAAiB5V,KAAKuC,QAAQqT,cAAc/T,OAAS,KACtFgU,EAAA,IACD7V,KAAKuC,QAAQqT,eAAiBF,EADjCA,EACqCG,GAGhC7V,KAAKoV,IAAIM,EAAM,EAAG,IAAIvO,KALkE,IAAD0O,CAOlG,EAEAnB,GAAQ/T,UAAUyU,IAAM,SAASM,EAAMR,EAAOC,GAC5C,IAAIxR,EAAU,GACVwD,EAAM,GACJa,EAAQmN,EAAOW,KAAK,KAC1B,IAAK,IAAI3V,KAAOuV,EACd,GAAIrV,OAAOM,UAAUC,eAAeC,KAAK6U,EAAMvV,GAC/C,QAAyB,IAAduV,EAAKvV,GAEVH,KAAK2U,YAAYxU,KACnBgH,GAAO,SAEJ,GAAkB,OAAduO,EAAKvV,GAEVH,KAAK2U,YAAYxU,IAEVA,IAAQH,KAAKuC,QAAQsE,cAD9BM,GAAO,GAGa,MAAXhH,EAAI,GACbgH,GAAOnH,KAAK8U,UAAUI,GAAS,IAAM/U,EAAM,IAAMH,KAAK+U,WAEtD5N,GAAOnH,KAAK8U,UAAUI,GAAS,IAAM/U,EAAM,IAAMH,KAAK+U,gBAGnD,GAAIW,EAAKvV,aAAgB4V,KAC9B5O,GAAOnH,KAAKsV,iBAAiBI,EAAKvV,GAAMA,EAAK,GAAI+U,QAC5C,GAAyB,iBAAdQ,EAAKvV,GAAmB,CAExC,IAAMiU,EAAOpU,KAAK2U,YAAYxU,GAC9B,GAAIiU,IAASpU,KAAKgN,mBAAmBoH,EAAMpM,GACzCrE,GAAW3D,KAAKgW,iBAAiB5B,EAAM,GAAKsB,EAAKvV,SAC5C,IAAKiU,EAEV,GAAIjU,IAAQH,KAAKuC,QAAQgE,aAAc,CACrC,IAAIiH,EAASxN,KAAKuC,QAAQ2E,kBAAkB/G,EAAK,GAAKuV,EAAKvV,IAC3DgH,GAAOnH,KAAK6M,qBAAqBW,EACnC,MACErG,GAAOnH,KAAKsV,iBAAiBI,EAAKvV,GAAMA,EAAK,GAAI+U,EAGvD,MAAO,GAAI7J,MAAM9D,QAAQmO,EAAKvV,IAAO,CAKnC,IAHA,IAAM8V,EAASP,EAAKvV,GAAK0B,OACrBqU,EAAa,GACbC,EAAc,GACTtL,EAAI,EAAGA,EAAIoL,EAAQpL,IAAK,CAC/B,IAAMuL,EAAOV,EAAKvV,GAAK0K,GACvB,QAAoB,IAATuL,QAEJ,GAAa,OAATA,EACK,MAAXjW,EAAI,GAAYgH,GAAOnH,KAAK8U,UAAUI,GAAS,IAAM/U,EAAM,IAAMH,KAAK+U,WACpE5N,GAAOnH,KAAK8U,UAAUI,GAAS,IAAM/U,EAAM,IAAMH,KAAK+U,gBAEtD,GAAoB,iBAATqB,EAChB,GAAGpW,KAAKuC,QAAQkS,aAAa,CAC3B,IAAMhR,EAASzD,KAAKoV,IAAIgB,EAAMlB,EAAQ,EAAGC,EAAOE,OAAOlV,IACvD+V,GAAczS,EAAO0D,IACjBnH,KAAKuC,QAAQ+D,qBAAuB8P,EAAKxV,eAAeZ,KAAKuC,QAAQ+D,uBACvE6P,GAAe1S,EAAOE,QAE1B,MACEuS,GAAclW,KAAK6U,qBAAqBuB,EAAMjW,EAAK+U,EAAOC,QAG5D,GAAInV,KAAKuC,QAAQkS,aAAc,CAC7B,IAAIF,EAAYvU,KAAKuC,QAAQ2E,kBAAkB/G,EAAKiW,GAEpDF,GADA3B,EAAYvU,KAAK6M,qBAAqB0H,EAExC,MACE2B,GAAclW,KAAKsV,iBAAiBc,EAAMjW,EAAK,GAAI+U,EAGzD,CACGlV,KAAKuC,QAAQkS,eACdyB,EAAalW,KAAKuV,gBAAgBW,EAAY/V,EAAKgW,EAAajB,IAElE/N,GAAO+O,CACT,MAEE,GAAIlW,KAAKuC,QAAQ+D,qBAAuBnG,IAAQH,KAAKuC,QAAQ+D,oBAG3D,IAFA,IAAM+P,EAAKhW,OAAOuI,KAAK8M,EAAKvV,IACtBmW,EAAID,EAAGxU,OACJgJ,EAAI,EAAGA,EAAIyL,EAAGzL,IACrBlH,GAAW3D,KAAKgW,iBAAiBK,EAAGxL,GAAI,GAAK6K,EAAKvV,GAAKkW,EAAGxL,UAG5D1D,GAAOnH,KAAK6U,qBAAqBa,EAAKvV,GAAMA,EAAK+U,EAAOC,GAI9D,MAAO,CAACxR,QAASA,EAASwD,IAAKA,EACjC,EAEAuN,GAAQ/T,UAAUqV,iBAAmB,SAAStQ,EAAUyB,GAGtD,OAFAA,EAAMnH,KAAKuC,QAAQ6E,wBAAwB1B,EAAU,GAAKyB,GAC1DA,EAAMnH,KAAK6M,qBAAqB1F,GAC5BnH,KAAKuC,QAAQ+R,2BAAqC,SAARnN,EACrC,IAAMzB,EACD,IAAMA,EAAW,KAAOyB,EAAM,GAC9C,EAWAuN,GAAQ/T,UAAU4U,gBAAkB,SAASpO,EAAKhH,EAAKwD,EAASuR,GAC9D,GAAW,KAAR/N,EACD,MAAc,MAAXhH,EAAI,GAAoBH,KAAK8U,UAAUI,GAAS,IAAM/U,EAAMwD,EAAS,IAAM3D,KAAK+U,WAE1E/U,KAAK8U,UAAUI,GAAS,IAAM/U,EAAMwD,EAAU3D,KAAKuW,SAASpW,GAAOH,KAAK+U,WAIjF,IAAIyB,EAAY,KAAOrW,EAAMH,KAAK+U,WAC9B0B,EAAgB,GAQpB,MANc,MAAXtW,EAAI,KACLsW,EAAgB,IAChBD,EAAY,KAIT7S,GAAuB,KAAZA,IAAyC,IAAtBwD,EAAI5C,QAAQ,MAEH,IAAjCvE,KAAKuC,QAAQiF,iBAA6BrH,IAAQH,KAAKuC,QAAQiF,iBAA4C,IAAzBiP,EAAc5U,OAClG7B,KAAK8U,UAAUI,GAAM,UAAU/N,EAAG,SAAQnH,KAAKgV,QAGpDhV,KAAK8U,UAAUI,GAAS,IAAM/U,EAAMwD,EAAU8S,EAAgBzW,KAAK+U,WACnE5N,EACAnH,KAAK8U,UAAUI,GAASsB,EAPjBxW,KAAK8U,UAAUI,GAAS,IAAO/U,EAAMwD,EAAU8S,EAAgB,IAAMtP,EAAMqP,CAU1F,EAEA9B,GAAQ/T,UAAU4V,SAAW,SAASpW,GACpC,IAAIoW,EAAW,GAQf,OAP+C,IAA5CvW,KAAKuC,QAAQH,aAAamC,QAAQpE,GAC/BH,KAAKuC,QAAQoR,uBAAsB4C,EAAW,KAElDA,EADOvW,KAAKuC,QAAQqR,kBACT,IAEH,MAASzT,EAEZoW,CACT,EAcA7B,GAAQ/T,UAAU2U,iBAAmB,SAASnO,EAAKhH,EAAKwD,EAASuR,GAC/D,IAAmC,IAA/BlV,KAAKuC,QAAQsE,eAA2B1G,IAAQH,KAAKuC,QAAQsE,cAC/D,OAAO7G,KAAK8U,UAAUI,GAAM,YAAe/N,EAAG,MAASnH,KAAKgV,QACxD,IAAqC,IAAjChV,KAAKuC,QAAQiF,iBAA6BrH,IAAQH,KAAKuC,QAAQiF,gBACvE,OAAOxH,KAAK8U,UAAUI,GAAM,UAAU/N,EAAG,SAASnH,KAAKgV,QACnD,GAAc,MAAX7U,EAAI,GACX,OAAQH,KAAK8U,UAAUI,GAAS,IAAM/U,EAAMwD,EAAS,IAAM3D,KAAK+U,WAEhE,IAAIR,EAAYvU,KAAKuC,QAAQ2E,kBAAkB/G,EAAKgH,GAGpD,MAAkB,MAFlBoN,EAAYvU,KAAK6M,qBAAqB0H,IAG7BvU,KAAK8U,UAAUI,GAAS,IAAM/U,EAAMwD,EAAU3D,KAAKuW,SAASpW,GAAOH,KAAK+U,WAExE/U,KAAK8U,UAAUI,GAAS,IAAM/U,EAAMwD,EAAU,IAClD4Q,EACD,KAAOpU,EAAMH,KAAK+U,UAG1B,EAEAL,GAAQ/T,UAAUkM,qBAAuB,SAAS0H,GAChD,GAAGA,GAAaA,EAAU1S,OAAS,GAAK7B,KAAKuC,QAAQkF,gBACnD,IAAK,IAAI5E,EAAE,EAAGA,EAAE7C,KAAKuC,QAAQwG,SAASlH,OAAQgB,IAAK,CACjD,IAAMwM,EAASrP,KAAKuC,QAAQwG,SAASlG,GACrC0R,EAAYA,EAAUzP,QAAQuK,EAAO/N,MAAO+N,EAAOlI,IACrD,CAEF,OAAOoN,CACT,ECzQA,IAAMmC,GAAe,CACnBrU,SAAUA,G", "sources": ["webpack://fxp/webpack/universalModuleDefinition", "webpack://fxp/webpack/bootstrap", "webpack://fxp/webpack/runtime/define property getters", "webpack://fxp/webpack/runtime/hasOwnProperty shorthand", "webpack://fxp/webpack/runtime/make namespace object", "webpack://fxp/./src/util.js", "webpack://fxp/./src/validator.js", "webpack://fxp/./src/xmlparser/OptionsBuilder.js", "webpack://fxp/./src/xmlparser/xmlNode.js", "webpack://fxp/./src/xmlparser/DocTypeReader.js", "webpack://fxp/./node_modules/strnum/strnum.js", "webpack://fxp/./src/ignoreAttributes.js", "webpack://fxp/./src/xmlparser/OrderedObjParser.js", "webpack://fxp/./src/xmlparser/node2json.js", "webpack://fxp/./src/xmlparser/XMLParser.js", "webpack://fxp/./src/xmlbuilder/orderedJs2Xml.js", "webpack://fxp/./src/xmlbuilder/json2xml.js", "webpack://fxp/./src/fxp.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"fxp\"] = factory();\n\telse\n\t\troot[\"fxp\"] = factory();\n})(this, () => {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "'use strict';\n\nconst nameStartChar = ':A-Za-z_\\\\u00C0-\\\\u00D6\\\\u00D8-\\\\u00F6\\\\u00F8-\\\\u02FF\\\\u0370-\\\\u037D\\\\u037F-\\\\u1FFF\\\\u200C-\\\\u200D\\\\u2070-\\\\u218F\\\\u2C00-\\\\u2FEF\\\\u3001-\\\\uD7FF\\\\uF900-\\\\uFDCF\\\\uFDF0-\\\\uFFFD';\nconst nameChar = nameStartChar + '\\\\-.\\\\d\\\\u00B7\\\\u0300-\\\\u036F\\\\u203F-\\\\u2040';\nexport const nameRegexp = '[' + nameStartChar + '][' + nameChar + ']*';\nconst regexName = new RegExp('^' + nameRegexp + '$');\n\nexport function getAllMatches(string, regex) {\n  const matches = [];\n  let match = regex.exec(string);\n  while (match) {\n    const allmatches = [];\n    allmatches.startIndex = regex.lastIndex - match[0].length;\n    const len = match.length;\n    for (let index = 0; index < len; index++) {\n      allmatches.push(match[index]);\n    }\n    matches.push(allmatches);\n    match = regex.exec(string);\n  }\n  return matches;\n}\n\nexport const isName = function(string) {\n  const match = regexName.exec(string);\n  return !(match === null || typeof match === 'undefined');\n}\n\nexport function isExist(v) {\n  return typeof v !== 'undefined';\n}\n\nexport function isEmptyObject(obj) {\n  return Object.keys(obj).length === 0;\n}\n\n/**\n * Copy all the properties of a into b.\n * @param {*} target\n * @param {*} a\n */\nexport function merge(target, a, arrayMode) {\n  if (a) {\n    const keys = Object.keys(a); // will return an array of own properties\n    const len = keys.length; //don't make it inline\n    for (let i = 0; i < len; i++) {\n      if (arrayMode === 'strict') {\n        target[keys[i]] = [ a[keys[i]] ];\n      } else {\n        target[keys[i]] = a[keys[i]];\n      }\n    }\n  }\n}\n/* exports.merge =function (b,a){\n  return Object.assign(b,a);\n} */\n\nexport function getValue(v) {\n  if (exports.isExist(v)) {\n    return v;\n  } else {\n    return '';\n  }\n}\n\n// const fakeCall = function(a) {return a;};\n// const fakeCallNoReturn = function() {};", "'use strict';\n\nimport {getAllMatches, isName} from './util.js';\n\nconst defaultOptions = {\n  allowBooleanAttributes: false, //A tag can have attributes without any value\n  unpairedTags: []\n};\n\n//const tagsPattern = new RegExp(\"<\\\\/?([\\\\w:\\\\-_\\.]+)\\\\s*\\/?>\",\"g\");\nexport function validate(xmlData, options) {\n  options = Object.assign({}, defaultOptions, options);\n\n  //xmlData = xmlData.replace(/(\\r\\n|\\n|\\r)/gm,\"\");//make it single line\n  //xmlData = xmlData.replace(/(^\\s*<\\?xml.*?\\?>)/g,\"\");//Remove XML starting tag\n  //xmlData = xmlData.replace(/(<!DOCTYPE[\\s\\w\\\"\\.\\/\\-\\:]+(\\[.*\\])*\\s*>)/g,\"\");//Remove DOCTYPE\n  const tags = [];\n  let tagFound = false;\n\n  //indicates that the root tag has been closed (aka. depth 0 has been reached)\n  let reachedRoot = false;\n\n  if (xmlData[0] === '\\ufeff') {\n    // check for byte order mark (BOM)\n    xmlData = xmlData.substr(1);\n  }\n  \n  for (let i = 0; i < xmlData.length; i++) {\n\n    if (xmlData[i] === '<' && xmlData[i+1] === '?') {\n      i+=2;\n      i = readPI(xmlData,i);\n      if (i.err) return i;\n    }else if (xmlData[i] === '<') {\n      //starting of tag\n      //read until you reach to '>' avoiding any '>' in attribute value\n      let tagStartPos = i;\n      i++;\n      \n      if (xmlData[i] === '!') {\n        i = readCommentAndCDATA(xmlData, i);\n        continue;\n      } else {\n        let closingTag = false;\n        if (xmlData[i] === '/') {\n          //closing tag\n          closingTag = true;\n          i++;\n        }\n        //read tagname\n        let tagName = '';\n        for (; i < xmlData.length &&\n          xmlData[i] !== '>' &&\n          xmlData[i] !== ' ' &&\n          xmlData[i] !== '\\t' &&\n          xmlData[i] !== '\\n' &&\n          xmlData[i] !== '\\r'; i++\n        ) {\n          tagName += xmlData[i];\n        }\n        tagName = tagName.trim();\n        //console.log(tagName);\n\n        if (tagName[tagName.length - 1] === '/') {\n          //self closing tag without attributes\n          tagName = tagName.substring(0, tagName.length - 1);\n          //continue;\n          i--;\n        }\n        if (!validateTagName(tagName)) {\n          let msg;\n          if (tagName.trim().length === 0) {\n            msg = \"Invalid space after '<'.\";\n          } else {\n            msg = \"Tag '\"+tagName+\"' is an invalid name.\";\n          }\n          return getErrorObject('InvalidTag', msg, getLineNumberForPosition(xmlData, i));\n        }\n\n        const result = readAttributeStr(xmlData, i);\n        if (result === false) {\n          return getErrorObject('InvalidAttr', \"Attributes for '\"+tagName+\"' have open quote.\", getLineNumberForPosition(xmlData, i));\n        }\n        let attrStr = result.value;\n        i = result.index;\n\n        if (attrStr[attrStr.length - 1] === '/') {\n          //self closing tag\n          const attrStrStart = i - attrStr.length;\n          attrStr = attrStr.substring(0, attrStr.length - 1);\n          const isValid = validateAttributeString(attrStr, options);\n          if (isValid === true) {\n            tagFound = true;\n            //continue; //text may presents after self closing tag\n          } else {\n            //the result from the nested function returns the position of the error within the attribute\n            //in order to get the 'true' error line, we need to calculate the position where the attribute begins (i - attrStr.length) and then add the position within the attribute\n            //this gives us the absolute index in the entire xml, which we can use to find the line at last\n            return getErrorObject(isValid.err.code, isValid.err.msg, getLineNumberForPosition(xmlData, attrStrStart + isValid.err.line));\n          }\n        } else if (closingTag) {\n          if (!result.tagClosed) {\n            return getErrorObject('InvalidTag', \"Closing tag '\"+tagName+\"' doesn't have proper closing.\", getLineNumberForPosition(xmlData, i));\n          } else if (attrStr.trim().length > 0) {\n            return getErrorObject('InvalidTag', \"Closing tag '\"+tagName+\"' can't have attributes or invalid starting.\", getLineNumberForPosition(xmlData, tagStartPos));\n          } else if (tags.length === 0) {\n            return getErrorObject('InvalidTag', \"Closing tag '\"+tagName+\"' has not been opened.\", getLineNumberForPosition(xmlData, tagStartPos));\n          } else {\n            const otg = tags.pop();\n            if (tagName !== otg.tagName) {\n              let openPos = getLineNumberForPosition(xmlData, otg.tagStartPos);\n              return getErrorObject('InvalidTag',\n                \"Expected closing tag '\"+otg.tagName+\"' (opened in line \"+openPos.line+\", col \"+openPos.col+\") instead of closing tag '\"+tagName+\"'.\",\n                getLineNumberForPosition(xmlData, tagStartPos));\n            }\n\n            //when there are no more tags, we reached the root level.\n            if (tags.length == 0) {\n              reachedRoot = true;\n            }\n          }\n        } else {\n          const isValid = validateAttributeString(attrStr, options);\n          if (isValid !== true) {\n            //the result from the nested function returns the position of the error within the attribute\n            //in order to get the 'true' error line, we need to calculate the position where the attribute begins (i - attrStr.length) and then add the position within the attribute\n            //this gives us the absolute index in the entire xml, which we can use to find the line at last\n            return getErrorObject(isValid.err.code, isValid.err.msg, getLineNumberForPosition(xmlData, i - attrStr.length + isValid.err.line));\n          }\n\n          //if the root level has been reached before ...\n          if (reachedRoot === true) {\n            return getErrorObject('InvalidXml', 'Multiple possible root nodes found.', getLineNumberForPosition(xmlData, i));\n          } else if(options.unpairedTags.indexOf(tagName) !== -1){\n            //don't push into stack\n          } else {\n            tags.push({tagName, tagStartPos});\n          }\n          tagFound = true;\n        }\n\n        //skip tag text value\n        //It may include comments and CDATA value\n        for (i++; i < xmlData.length; i++) {\n          if (xmlData[i] === '<') {\n            if (xmlData[i + 1] === '!') {\n              //comment or CADATA\n              i++;\n              i = readCommentAndCDATA(xmlData, i);\n              continue;\n            } else if (xmlData[i+1] === '?') {\n              i = readPI(xmlData, ++i);\n              if (i.err) return i;\n            } else{\n              break;\n            }\n          } else if (xmlData[i] === '&') {\n            const afterAmp = validateAmpersand(xmlData, i);\n            if (afterAmp == -1)\n              return getErrorObject('InvalidChar', \"char '&' is not expected.\", getLineNumberForPosition(xmlData, i));\n            i = afterAmp;\n          }else{\n            if (reachedRoot === true && !isWhiteSpace(xmlData[i])) {\n              return getErrorObject('InvalidXml', \"Extra text at the end\", getLineNumberForPosition(xmlData, i));\n            }\n          }\n        } //end of reading tag text value\n        if (xmlData[i] === '<') {\n          i--;\n        }\n      }\n    } else {\n      if ( isWhiteSpace(xmlData[i])) {\n        continue;\n      }\n      return getErrorObject('InvalidChar', \"char '\"+xmlData[i]+\"' is not expected.\", getLineNumberForPosition(xmlData, i));\n    }\n  }\n\n  if (!tagFound) {\n    return getErrorObject('InvalidXml', 'Start tag expected.', 1);\n  }else if (tags.length == 1) {\n      return getErrorObject('InvalidTag', \"Unclosed tag '\"+tags[0].tagName+\"'.\", getLineNumberForPosition(xmlData, tags[0].tagStartPos));\n  }else if (tags.length > 0) {\n      return getErrorObject('InvalidXml', \"Invalid '\"+\n          JSON.stringify(tags.map(t => t.tagName), null, 4).replace(/\\r?\\n/g, '')+\n          \"' found.\", {line: 1, col: 1});\n  }\n\n  return true;\n};\n\nfunction isWhiteSpace(char){\n  return char === ' ' || char === '\\t' || char === '\\n'  || char === '\\r';\n}\n/**\n * Read Processing insstructions and skip\n * @param {*} xmlData\n * @param {*} i\n */\nfunction readPI(xmlData, i) {\n  const start = i;\n  for (; i < xmlData.length; i++) {\n    if (xmlData[i] == '?' || xmlData[i] == ' ') {\n      //tagname\n      const tagname = xmlData.substr(start, i - start);\n      if (i > 5 && tagname === 'xml') {\n        return getErrorObject('InvalidXml', 'XML declaration allowed only at the start of the document.', getLineNumberForPosition(xmlData, i));\n      } else if (xmlData[i] == '?' && xmlData[i + 1] == '>') {\n        //check if valid attribut string\n        i++;\n        break;\n      } else {\n        continue;\n      }\n    }\n  }\n  return i;\n}\n\nfunction readCommentAndCDATA(xmlData, i) {\n  if (xmlData.length > i + 5 && xmlData[i + 1] === '-' && xmlData[i + 2] === '-') {\n    //comment\n    for (i += 3; i < xmlData.length; i++) {\n      if (xmlData[i] === '-' && xmlData[i + 1] === '-' && xmlData[i + 2] === '>') {\n        i += 2;\n        break;\n      }\n    }\n  } else if (\n    xmlData.length > i + 8 &&\n    xmlData[i + 1] === 'D' &&\n    xmlData[i + 2] === 'O' &&\n    xmlData[i + 3] === 'C' &&\n    xmlData[i + 4] === 'T' &&\n    xmlData[i + 5] === 'Y' &&\n    xmlData[i + 6] === 'P' &&\n    xmlData[i + 7] === 'E'\n  ) {\n    let angleBracketsCount = 1;\n    for (i += 8; i < xmlData.length; i++) {\n      if (xmlData[i] === '<') {\n        angleBracketsCount++;\n      } else if (xmlData[i] === '>') {\n        angleBracketsCount--;\n        if (angleBracketsCount === 0) {\n          break;\n        }\n      }\n    }\n  } else if (\n    xmlData.length > i + 9 &&\n    xmlData[i + 1] === '[' &&\n    xmlData[i + 2] === 'C' &&\n    xmlData[i + 3] === 'D' &&\n    xmlData[i + 4] === 'A' &&\n    xmlData[i + 5] === 'T' &&\n    xmlData[i + 6] === 'A' &&\n    xmlData[i + 7] === '['\n  ) {\n    for (i += 8; i < xmlData.length; i++) {\n      if (xmlData[i] === ']' && xmlData[i + 1] === ']' && xmlData[i + 2] === '>') {\n        i += 2;\n        break;\n      }\n    }\n  }\n\n  return i;\n}\n\nconst doubleQuote = '\"';\nconst singleQuote = \"'\";\n\n/**\n * Keep reading xmlData until '<' is found outside the attribute value.\n * @param {string} xmlData\n * @param {number} i\n */\nfunction readAttributeStr(xmlData, i) {\n  let attrStr = '';\n  let startChar = '';\n  let tagClosed = false;\n  for (; i < xmlData.length; i++) {\n    if (xmlData[i] === doubleQuote || xmlData[i] === singleQuote) {\n      if (startChar === '') {\n        startChar = xmlData[i];\n      } else if (startChar !== xmlData[i]) {\n        //if vaue is enclosed with double quote then single quotes are allowed inside the value and vice versa\n      } else {\n        startChar = '';\n      }\n    } else if (xmlData[i] === '>') {\n      if (startChar === '') {\n        tagClosed = true;\n        break;\n      }\n    }\n    attrStr += xmlData[i];\n  }\n  if (startChar !== '') {\n    return false;\n  }\n\n  return {\n    value: attrStr,\n    index: i,\n    tagClosed: tagClosed\n  };\n}\n\n/**\n * Select all the attributes whether valid or invalid.\n */\nconst validAttrStrRegxp = new RegExp('(\\\\s*)([^\\\\s=]+)(\\\\s*=)?(\\\\s*([\\'\"])(([\\\\s\\\\S])*?)\\\\5)?', 'g');\n\n//attr, =\"sd\", a=\"amit's\", a=\"sd\"b=\"saf\", ab  cd=\"\"\n\nfunction validateAttributeString(attrStr, options) {\n  //console.log(\"start:\"+attrStr+\":end\");\n\n  //if(attrStr.trim().length === 0) return true; //empty string\n\n  const matches = getAllMatches(attrStr, validAttrStrRegxp);\n  const attrNames = {};\n\n  for (let i = 0; i < matches.length; i++) {\n    if (matches[i][1].length === 0) {\n      //nospace before attribute name: a=\"sd\"b=\"saf\"\n      return getErrorObject('InvalidAttr', \"Attribute '\"+matches[i][2]+\"' has no space in starting.\", getPositionFromMatch(matches[i]))\n    } else if (matches[i][3] !== undefined && matches[i][4] === undefined) {\n      return getErrorObject('InvalidAttr', \"Attribute '\"+matches[i][2]+\"' is without value.\", getPositionFromMatch(matches[i]));\n    } else if (matches[i][3] === undefined && !options.allowBooleanAttributes) {\n      //independent attribute: ab\n      return getErrorObject('InvalidAttr', \"boolean attribute '\"+matches[i][2]+\"' is not allowed.\", getPositionFromMatch(matches[i]));\n    }\n    /* else if(matches[i][6] === undefined){//attribute without value: ab=\n                    return { err: { code:\"InvalidAttr\",msg:\"attribute \" + matches[i][2] + \" has no value assigned.\"}};\n                } */\n    const attrName = matches[i][2];\n    if (!validateAttrName(attrName)) {\n      return getErrorObject('InvalidAttr', \"Attribute '\"+attrName+\"' is an invalid name.\", getPositionFromMatch(matches[i]));\n    }\n    if (!attrNames.hasOwnProperty(attrName)) {\n      //check for duplicate attribute.\n      attrNames[attrName] = 1;\n    } else {\n      return getErrorObject('InvalidAttr', \"Attribute '\"+attrName+\"' is repeated.\", getPositionFromMatch(matches[i]));\n    }\n  }\n\n  return true;\n}\n\nfunction validateNumberAmpersand(xmlData, i) {\n  let re = /\\d/;\n  if (xmlData[i] === 'x') {\n    i++;\n    re = /[\\da-fA-F]/;\n  }\n  for (; i < xmlData.length; i++) {\n    if (xmlData[i] === ';')\n      return i;\n    if (!xmlData[i].match(re))\n      break;\n  }\n  return -1;\n}\n\nfunction validateAmpersand(xmlData, i) {\n  // https://www.w3.org/TR/xml/#dt-charref\n  i++;\n  if (xmlData[i] === ';')\n    return -1;\n  if (xmlData[i] === '#') {\n    i++;\n    return validateNumberAmpersand(xmlData, i);\n  }\n  let count = 0;\n  for (; i < xmlData.length; i++, count++) {\n    if (xmlData[i].match(/\\w/) && count < 20)\n      continue;\n    if (xmlData[i] === ';')\n      break;\n    return -1;\n  }\n  return i;\n}\n\nfunction getErrorObject(code, message, lineNumber) {\n  return {\n    err: {\n      code: code,\n      msg: message,\n      line: lineNumber.line || lineNumber,\n      col: lineNumber.col,\n    },\n  };\n}\n\nfunction validateAttrName(attrName) {\n  return isName(attrName);\n}\n\n// const startsWithXML = /^xml/i;\n\nfunction validateTagName(tagname) {\n  return isName(tagname) /* && !tagname.match(startsWithXML) */;\n}\n\n//this function returns the line number for the character at the given index\nfunction getLineNumberForPosition(xmlData, index) {\n  const lines = xmlData.substring(0, index).split(/\\r?\\n/);\n  return {\n    line: lines.length,\n\n    // column number is last line's length + 1, because column numbering starts at 1:\n    col: lines[lines.length - 1].length + 1\n  };\n}\n\n//this function returns the position of the first character of match within attrStr\nfunction getPositionFromMatch(match) {\n  return match.startIndex + match[1].length;\n}\n", "\nexport const defaultOptions = {\n    preserveOrder: false,\n    attributeNamePrefix: '@_',\n    attributesGroupName: false,\n    textNodeName: '#text',\n    ignoreAttributes: true,\n    removeNSPrefix: false, // remove NS from tag name or attribute name if true\n    allowBooleanAttributes: false, //a tag can have attributes without any value\n    //ignoreRootElement : false,\n    parseTagValue: true,\n    parseAttributeValue: false,\n    trimValues: true, //Trim string values of tag and attributes\n    cdataPropName: false,\n    numberParseOptions: {\n      hex: true,\n      leadingZeros: true,\n      eNotation: true\n    },\n    tagValueProcessor: function(tagName, val) {\n      return val;\n    },\n    attributeValueProcessor: function(attrName, val) {\n      return val;\n    },\n    stopNodes: [], //nested tags will not be parsed even for errors\n    alwaysCreateTextNode: false,\n    isArray: () => false,\n    commentPropName: false,\n    unpairedTags: [],\n    processEntities: true,\n    htmlEntities: false,\n    ignoreDeclaration: false,\n    ignorePiTags: false,\n    transformTagName: false,\n    transformAttributeName: false,\n    updateTag: function(tagName, jPath, attrs){\n      return tagName\n    },\n    // skipEmptyListItem: false\n    captureMetaData: false,\n};\n   \nexport const buildOptions = function(options) {\n    return Object.assign({}, defaultOptions, options);\n};\n", "'use strict';\n\nlet METADATA_SYMBOL;\n\nif (typeof Symbol !== \"function\") {\n  METADATA_SYMBOL = \"@@xmlMetadata\";\n} else {\n  METADATA_SYMBOL = Symbol(\"XML Node Metadata\");\n}\n\nexport default class XmlNode{\n  constructor(tagname) {\n    this.tagname = tagname;\n    this.child = []; //nested tags, text, cdata, comments in order\n    this[\":@\"] = {}; //attributes map\n  }\n  add(key,val){\n    // this.child.push( {name : key, val: val, isCdata: isCdata });\n    if(key === \"__proto__\") key = \"#__proto__\";\n    this.child.push( {[key]: val });\n  }\n  addChild(node, startIndex) {\n    if(node.tagname === \"__proto__\") node.tagname = \"#__proto__\";\n    if(node[\":@\"] && Object.keys(node[\":@\"]).length > 0){\n      this.child.push( { [node.tagname]: node.child, [\":@\"]: node[\":@\"] });\n    }else{\n      this.child.push( { [node.tagname]: node.child });\n    }\n    // if requested, add the startIndex\n    if (startIndex !== undefined) {\n      // Note: for now we just overwrite the metadata. If we had more complex metadata,\n      // we might need to do an object append here:  metadata = { ...metadata, startIndex }\n      this.child[this.child.length - 1][METADATA_SYMBOL] = { startIndex };\n    }\n  }\n  /** symbol used for metadata */\n  static getMetaDataSymbol() {\n    return METADATA_SYMBOL;\n  }\n}\n", "import {isName} from '../util.js';\n\n//TODO: handle comments\nexport default function readDocType(xmlData, i){\n    \n    const entities = {};\n    if( xmlData[i + 3] === 'O' &&\n         xmlData[i + 4] === 'C' &&\n         xmlData[i + 5] === 'T' &&\n         xmlData[i + 6] === 'Y' &&\n         xmlData[i + 7] === 'P' &&\n         xmlData[i + 8] === 'E')\n    {    \n        i = i+9;\n        let angleBracketsCount = 1;\n        let hasBody = false, comment = false;\n        let exp = \"\";\n        for(;i<xmlData.length;i++){\n            if (xmlData[i] === '<' && !comment) { //Determine the tag type\n                if( hasBody && hasSeq(xmlData, \"!ENTITY\",i)){\n                    i += 7; \n                    let entityName, val;\n                    [entityName, val,i] = readEntityExp(xmlData,i+1);\n                    if(val.indexOf(\"&\") === -1) //Parameter entities are not supported\n                        entities[ entityName ] = {\n                            regx : RegExp( `&${entityName};`,\"g\"),\n                            val: val\n                        };\n                }\n                else if( hasBody && hasSeq(xmlData, \"!ELEMENT\",i))  {\n                    i += 8;//Not supported\n                    const {index} = readElementExp(xmlData,i+1);\n                    i = index;\n                }else if( hasBody && hasSeq(xmlData, \"!ATTLIST\",i)){\n                    i += 8;//Not supported\n                    // const {index} = readAttlistExp(xmlData,i+1);\n                    // i = index;\n                }else if( hasBody && hasSeq(xmlData, \"!NOTATION\",i)) {\n                    i += 9;//Not supported\n                    const {index} = readNotationExp(xmlData,i+1);\n                    i = index;\n                }else if( hasSeq(xmlData, \"!--\",i) ) comment = true;\n                else throw new Error(\"Invalid DOCTYPE\");\n\n                angleBracketsCount++;\n                exp = \"\";\n            } else if (xmlData[i] === '>') { //Read tag content\n                if(comment){\n                    if( xmlData[i - 1] === \"-\" && xmlData[i - 2] === \"-\"){\n                        comment = false;\n                        angleBracketsCount--;\n                    }\n                }else{\n                    angleBracketsCount--;\n                }\n                if (angleBracketsCount === 0) {\n                  break;\n                }\n            }else if( xmlData[i] === '['){\n                hasBody = true;\n            }else{\n                exp += xmlData[i];\n            }\n        }\n        if(angleBracketsCount !== 0){\n            throw new Error(`Unclosed DOCTYPE`);\n        }\n    }else{\n        throw new Error(`Invalid Tag instead of DOCTYPE`);\n    }\n    return {entities, i};\n}\n\nconst skipWhitespace = (data, index) => {\n    while (index < data.length && /\\s/.test(data[index])) {\n        index++;\n    }\n    return index;\n};\n\nfunction readEntityExp(xmlData, i) {    \n    //External entities are not supported\n    //    <!ENTITY ext SYSTEM \"http://normal-website.com\" >\n\n    //Parameter entities are not supported\n    //    <!ENTITY entityname \"&anotherElement;\">\n\n    //Internal entities are supported\n    //    <!ENTITY entityname \"replacement text\">\n\n    // Skip leading whitespace after <!ENTITY\n    i = skipWhitespace(xmlData, i);\n\n    // Read entity name\n    let entityName = \"\";\n    while (i < xmlData.length && !/\\s/.test(xmlData[i]) && xmlData[i] !== '\"' && xmlData[i] !== \"'\") {\n        entityName += xmlData[i];\n        i++;\n    }\n    validateEntityName(entityName);\n\n    // Skip whitespace after entity name\n    i = skipWhitespace(xmlData, i);\n\n    // Check for unsupported constructs (external entities or parameter entities)\n    if (xmlData.substring(i, i + 6).toUpperCase() === \"SYSTEM\") {\n        throw new Error(\"External entities are not supported\");\n    }else if (xmlData[i] === \"%\") {\n        throw new Error(\"Parameter entities are not supported\");\n    }\n\n    // Read entity value (internal entity)\n    let entityValue = \"\";\n    [i, entityValue] = readIdentifierVal(xmlData, i, \"entity\");\n    i--;\n    return [entityName, entityValue, i ];\n}\n\nfunction readNotationExp(xmlData, i) {\n    // Skip leading whitespace after <!NOTATION\n    i = skipWhitespace(xmlData, i);\n\n    // Read notation name\n    let notationName = \"\";\n    while (i < xmlData.length && !/\\s/.test(xmlData[i])) {\n        notationName += xmlData[i];\n        i++;\n    }\n    validateEntityName(notationName);\n\n    // Skip whitespace after notation name\n    i = skipWhitespace(xmlData, i);\n\n    // Check identifier type (SYSTEM or PUBLIC)\n    const identifierType = xmlData.substring(i, i + 6).toUpperCase();\n    if (identifierType !== \"SYSTEM\" && identifierType !== \"PUBLIC\") {\n        throw new Error(`Expected SYSTEM or PUBLIC, found \"${identifierType}\"`);\n    }\n    i += identifierType.length;\n\n    // Skip whitespace after identifier type\n    i = skipWhitespace(xmlData, i);\n\n    // Read public identifier (if PUBLIC)\n    let publicIdentifier = null;\n    let systemIdentifier = null;\n\n    if (identifierType === \"PUBLIC\") {\n        [i, publicIdentifier ] = readIdentifierVal(xmlData, i, \"publicIdentifier\");\n\n        // Skip whitespace after public identifier\n        i = skipWhitespace(xmlData, i);\n\n        // Optionally read system identifier\n        if (xmlData[i] === '\"' || xmlData[i] === \"'\") {\n            [i, systemIdentifier ] = readIdentifierVal(xmlData, i,\"systemIdentifier\");\n        }\n    } else if (identifierType === \"SYSTEM\") {\n        // Read system identifier (mandatory for SYSTEM)\n        [i, systemIdentifier ] = readIdentifierVal(xmlData, i, \"systemIdentifier\");\n\n        if (!systemIdentifier) {\n            throw new Error(\"Missing mandatory system identifier for SYSTEM notation\");\n        }\n    }\n    \n    return {notationName, publicIdentifier, systemIdentifier, index: --i};\n}\n\nfunction readIdentifierVal(xmlData, i, type) {\n    let identifierVal = \"\";\n    const startChar = xmlData[i];\n    if (startChar !== '\"' && startChar !== \"'\") {\n        throw new Error(`Expected quoted string, found \"${startChar}\"`);\n    }\n    i++;\n\n    while (i < xmlData.length && xmlData[i] !== startChar) {\n        identifierVal += xmlData[i];\n        i++;\n    }\n\n    if (xmlData[i] !== startChar) {\n        throw new Error(`Unterminated ${type} value`);\n    }\n    i++;\n    return [i, identifierVal];\n}\n\nfunction readElementExp(xmlData, i) {\n    // <!ELEMENT br EMPTY>\n    // <!ELEMENT div ANY>\n    // <!ELEMENT title (#PCDATA)>\n    // <!ELEMENT book (title, author+)>\n    // <!ELEMENT name (content-model)>\n    \n    // Skip leading whitespace after <!ELEMENT\n    i = skipWhitespace(xmlData, i);\n\n    // Read element name\n    let elementName = \"\";\n    while (i < xmlData.length && !/\\s/.test(xmlData[i])) {\n        elementName += xmlData[i];\n        i++;\n    }\n\n    // Validate element name\n    if (!validateEntityName(elementName)) {\n        throw new Error(`Invalid element name: \"${elementName}\"`);\n    }\n\n    // Skip whitespace after element name\n    i = skipWhitespace(xmlData, i);\n    let contentModel = \"\";\n    // Expect '(' to start content model\n    if(xmlData[i] === \"E\" && hasSeq(xmlData, \"MPTY\",i)) i+=6;\n    else if(xmlData[i] === \"A\" && hasSeq(xmlData, \"NY\",i)) i+=4;\n    else if (xmlData[i] === \"(\") {\n        i++; // Move past '('\n\n        // Read content model\n        while (i < xmlData.length && xmlData[i] !== \")\") {\n            contentModel += xmlData[i];\n            i++;\n        }\n        if (xmlData[i] !== \")\") {\n            throw new Error(\"Unterminated content model\");\n        }\n\n    }else{\n        throw new Error(`Invalid Element Expression, found \"${xmlData[i]}\"`);\n    }\n    \n    return {\n        elementName,\n        contentModel: contentModel.trim(),\n        index: i\n    };\n}\n\nfunction readAttlistExp(xmlData, i) {\n    // Skip leading whitespace after <!ATTLIST\n    i = skipWhitespace(xmlData, i);\n\n    // Read element name\n    let elementName = \"\";\n    while (i < xmlData.length && !/\\s/.test(xmlData[i])) {\n        elementName += xmlData[i];\n        i++;\n    }\n\n    // Validate element name\n    validateEntityName(elementName)\n\n    // Skip whitespace after element name\n    i = skipWhitespace(xmlData, i);\n\n    // Read attribute name\n    let attributeName = \"\";\n    while (i < xmlData.length && !/\\s/.test(xmlData[i])) {\n        attributeName += xmlData[i];\n        i++;\n    }\n\n    // Validate attribute name\n    if (!validateEntityName(attributeName)) {\n        throw new Error(`Invalid attribute name: \"${attributeName}\"`);\n    }\n\n    // Skip whitespace after attribute name\n    i = skipWhitespace(xmlData, i);\n\n    // Read attribute type\n    let attributeType = \"\";\n    if (xmlData.substring(i, i + 8).toUpperCase() === \"NOTATION\") {\n        attributeType = \"NOTATION\";\n        i += 8; // Move past \"NOTATION\"\n\n        // Skip whitespace after \"NOTATION\"\n        i = skipWhitespace(xmlData, i);\n\n        // Expect '(' to start the list of notations\n        if (xmlData[i] !== \"(\") {\n            throw new Error(`Expected '(', found \"${xmlData[i]}\"`);\n        }\n        i++; // Move past '('\n\n        // Read the list of allowed notations\n        let allowedNotations = [];\n        while (i < xmlData.length && xmlData[i] !== \")\") {\n            let notation = \"\";\n            while (i < xmlData.length && xmlData[i] !== \"|\" && xmlData[i] !== \")\") {\n                notation += xmlData[i];\n                i++;\n            }\n\n            // Validate notation name\n            notation = notation.trim();\n            if (!validateEntityName(notation)) {\n                throw new Error(`Invalid notation name: \"${notation}\"`);\n            }\n\n            allowedNotations.push(notation);\n\n            // Skip '|' separator or exit loop\n            if (xmlData[i] === \"|\") {\n                i++; // Move past '|'\n                i = skipWhitespace(xmlData, i); // Skip optional whitespace after '|'\n            }\n        }\n\n        if (xmlData[i] !== \")\") {\n            throw new Error(\"Unterminated list of notations\");\n        }\n        i++; // Move past ')'\n\n        // Store the allowed notations as part of the attribute type\n        attributeType += \" (\" + allowedNotations.join(\"|\") + \")\";\n    } else {\n        // Handle simple types (e.g., CDATA, ID, IDREF, etc.)\n        while (i < xmlData.length && !/\\s/.test(xmlData[i])) {\n            attributeType += xmlData[i];\n            i++;\n        }\n\n        // Validate simple attribute type\n        const validTypes = [\"CDATA\", \"ID\", \"IDREF\", \"IDREFS\", \"ENTITY\", \"ENTITIES\", \"NMTOKEN\", \"NMTOKENS\"];\n        if (!validTypes.includes(attributeType.toUpperCase())) {\n            throw new Error(`Invalid attribute type: \"${attributeType}\"`);\n        }\n    }\n\n    // Skip whitespace after attribute type\n    i = skipWhitespace(xmlData, i);\n\n    // Read default value\n    let defaultValue = \"\";\n    if (xmlData.substring(i, i + 8).toUpperCase() === \"#REQUIRED\") {\n        defaultValue = \"#REQUIRED\";\n        i += 8;\n    } else if (xmlData.substring(i, i + 7).toUpperCase() === \"#IMPLIED\") {\n        defaultValue = \"#IMPLIED\";\n        i += 7;\n    } else {\n        [i, defaultValue] = readIdentifierVal(xmlData, i, \"ATTLIST\");\n    }\n\n    return {\n        elementName,\n        attributeName,\n        attributeType,\n        defaultValue,\n        index: i\n    }\n}\n\nfunction hasSeq(data, seq,i){\n    for(let j=0;j<seq.length;j++){\n        if(seq[j]!==data[i+j+1]) return false;\n    }\n    return true;\n}\n\nfunction validateEntityName(name){\n    if (isName(name))\n\treturn name;\n    else\n        throw new Error(`Invalid entity name ${name}`);\n}\n", "const hexRegex = /^[-+]?0x[a-fA-F0-9]+$/;\nconst numRegex = /^([\\-\\+])?(0*)([0-9]*(\\.[0-9]*)?)$/;\n// const octRegex = /^0x[a-z0-9]+/;\n// const binRegex = /0x[a-z0-9]+/;\n\n \nconst consider = {\n    hex :  true,\n    // oct: false,\n    leadingZeros: true,\n    decimalPoint: \"\\.\",\n    eNotation: true,\n    //skipLike: /regex/\n};\n\nexport default function toNumber(str, options = {}){\n    options = Object.assign({}, consider, options );\n    if(!str || typeof str !== \"string\" ) return str;\n    \n    let trimmedStr  = str.trim();\n    \n    if(options.skipLike !== undefined && options.skipLike.test(trimmedStr)) return str;\n    else if(str===\"0\") return 0;\n    else if (options.hex && hexRegex.test(trimmedStr)) {\n        return parse_int(trimmedStr, 16);\n    // }else if (options.oct && octRegex.test(str)) {\n    //     return Number.parseInt(val, 8);\n    }else if (trimmedStr.search(/.+[eE].+/)!== -1) { //eNotation\n        return resolveEnotation(str,trimmedStr,options);\n    // }else if (options.parseBin && binRegex.test(str)) {\n    //     return Number.parseInt(val, 2);\n    }else{\n        //separate negative sign, leading zeros, and rest number\n        const match = numRegex.exec(trimmedStr);\n        // +00.123 => [ , '+', '00', '.123', ..\n        if(match){\n            const sign = match[1] || \"\";\n            const leadingZeros = match[2];\n            let numTrimmedByZeros = trimZeros(match[3]); //complete num without leading zeros\n            const decimalAdjacentToLeadingZeros = sign ? // 0., -00., 000.\n                str[leadingZeros.length+1] === \".\" \n                : str[leadingZeros.length] === \".\";\n\n            //trim ending zeros for floating number\n            if(!options.leadingZeros //leading zeros are not allowed\n                && (leadingZeros.length > 1 \n                    || (leadingZeros.length === 1 && !decimalAdjacentToLeadingZeros))){\n                // 00, 00.3, +03.24, 03, 03.24\n                return str;\n            }\n            else{//no leading zeros or leading zeros are allowed\n                const num = Number(trimmedStr);\n                const parsedStr = String(num);\n\n                if( num === 0 || num === -0) return num;\n                if(parsedStr.search(/[eE]/) !== -1){ //given number is long and parsed to eNotation\n                    if(options.eNotation) return num;\n                    else return str;\n                }else if(trimmedStr.indexOf(\".\") !== -1){ //floating number\n                    if(parsedStr === \"0\") return num; //0.0\n                    else if(parsedStr === numTrimmedByZeros) return num; //0.456. 0.79000\n                    else if( parsedStr === `${sign}${numTrimmedByZeros}`) return num;\n                    else return str;\n                }\n                \n                let n = leadingZeros? numTrimmedByZeros : trimmedStr;\n                if(leadingZeros){\n                    // -009 => -9\n                    return (n === parsedStr) || (sign+n === parsedStr) ? num : str\n                }else  {\n                    // +9\n                    return (n === parsedStr) || (n === sign+parsedStr) ? num : str\n                }\n            }\n        }else{ //non-numeric string\n            return str;\n        }\n    }\n}\n\nconst eNotationRegx = /^([-+])?(0*)(\\d*(\\.\\d*)?[eE][-\\+]?\\d+)$/;\nfunction resolveEnotation(str,trimmedStr,options){\n    if(!options.eNotation) return str;\n    const notation = trimmedStr.match(eNotationRegx); \n    if(notation){\n        let sign = notation[1] || \"\";\n        const eChar = notation[3].indexOf(\"e\") === -1 ? \"E\" : \"e\";\n        const leadingZeros = notation[2];\n        const eAdjacentToLeadingZeros = sign ? // 0E.\n            str[leadingZeros.length+1] === eChar \n            : str[leadingZeros.length] === eChar;\n\n        if(leadingZeros.length > 1 && eAdjacentToLeadingZeros) return str;\n        else if(leadingZeros.length === 1 \n            && (notation[3].startsWith(`.${eChar}`) || notation[3][0] === eChar)){\n                return Number(trimmedStr);\n        }else if(options.leadingZeros && !eAdjacentToLeadingZeros){ //accept with leading zeros\n            //remove leading 0s\n            trimmedStr = (notation[1] || \"\") + notation[3];\n            return Number(trimmedStr);\n        }else return str;\n    }else{\n        return str;\n    }\n}\n\n/**\n * \n * @param {string} numStr without leading zeros\n * @returns \n */\nfunction trimZeros(numStr){\n    if(numStr && numStr.indexOf(\".\") !== -1){//float\n        numStr = numStr.replace(/0+$/, \"\"); //remove ending zeros\n        if(numStr === \".\")  numStr = \"0\";\n        else if(numStr[0] === \".\")  numStr = \"0\"+numStr;\n        else if(numStr[numStr.length-1] === \".\")  numStr = numStr.substring(0,numStr.length-1);\n        return numStr;\n    }\n    return numStr;\n}\n\nfunction parse_int(numStr, base){\n    //polyfill\n    if(parseInt) return parseInt(numStr, base);\n    else if(Number.parseInt) return Number.parseInt(numStr, base);\n    else if(window && window.parseInt) return window.parseInt(numStr, base);\n    else throw new Error(\"parseInt, Number.parseInt, window.parseInt are not supported\")\n}", "export default function getIgnoreAttributesFn(ignoreAttributes) {\n    if (typeof ignoreAttributes === 'function') {\n        return ignoreAttributes\n    }\n    if (Array.isArray(ignoreAttributes)) {\n        return (attrName) => {\n            for (const pattern of ignoreAttributes) {\n                if (typeof pattern === 'string' && attrName === pattern) {\n                    return true\n                }\n                if (pattern instanceof RegExp && pattern.test(attrName)) {\n                    return true\n                }\n            }\n        }\n    }\n    return () => false\n}", "'use strict';\n///@ts-check\n\nimport {getAllMatches, isExist} from '../util.js';\nimport xmlNode from './xmlNode.js';\nimport readDocType from './DocTypeReader.js';\nimport toNumber from \"strnum\";\nimport getIgnoreAttributesFn from \"../ignoreAttributes.js\";\n\n// const regx =\n//   '<((!\\\\[CDATA\\\\[([\\\\s\\\\S]*?)(]]>))|((NAME:)?(NAME))([^>]*)>|((\\\\/)(NAME)\\\\s*>))([^<]*)'\n//   .replace(/NAME/g, util.nameRegexp);\n\n//const tagsRegx = new RegExp(\"<(\\\\/?[\\\\w:\\\\-\\._]+)([^>]*)>(\\\\s*\"+cdataRegx+\")*([^<]+)?\",\"g\");\n//const tagsRegx = new RegExp(\"<(\\\\/?)((\\\\w*:)?([\\\\w:\\\\-\\._]+))([^>]*)>([^<]*)(\"+cdataRegx+\"([^<]*))*([^<]+)?\",\"g\");\n\nexport default class OrderedObjParser{\n  constructor(options){\n    this.options = options;\n    this.currentNode = null;\n    this.tagsNodeStack = [];\n    this.docTypeEntities = {};\n    this.lastEntities = {\n      \"apos\" : { regex: /&(apos|#39|#x27);/g, val : \"'\"},\n      \"gt\" : { regex: /&(gt|#62|#x3E);/g, val : \">\"},\n      \"lt\" : { regex: /&(lt|#60|#x3C);/g, val : \"<\"},\n      \"quot\" : { regex: /&(quot|#34|#x22);/g, val : \"\\\"\"},\n    };\n    this.ampEntity = { regex: /&(amp|#38|#x26);/g, val : \"&\"};\n    this.htmlEntities = {\n      \"space\": { regex: /&(nbsp|#160);/g, val: \" \" },\n      // \"lt\" : { regex: /&(lt|#60);/g, val: \"<\" },\n      // \"gt\" : { regex: /&(gt|#62);/g, val: \">\" },\n      // \"amp\" : { regex: /&(amp|#38);/g, val: \"&\" },\n      // \"quot\" : { regex: /&(quot|#34);/g, val: \"\\\"\" },\n      // \"apos\" : { regex: /&(apos|#39);/g, val: \"'\" },\n      \"cent\" : { regex: /&(cent|#162);/g, val: \"¢\" },\n      \"pound\" : { regex: /&(pound|#163);/g, val: \"£\" },\n      \"yen\" : { regex: /&(yen|#165);/g, val: \"¥\" },\n      \"euro\" : { regex: /&(euro|#8364);/g, val: \"€\" },\n      \"copyright\" : { regex: /&(copy|#169);/g, val: \"©\" },\n      \"reg\" : { regex: /&(reg|#174);/g, val: \"®\" },\n      \"inr\" : { regex: /&(inr|#8377);/g, val: \"₹\" },\n      \"num_dec\": { regex: /&#([0-9]{1,7});/g, val : (_, str) => String.fromCodePoint(Number.parseInt(str, 10)) },\n      \"num_hex\": { regex: /&#x([0-9a-fA-F]{1,6});/g, val : (_, str) => String.fromCodePoint(Number.parseInt(str, 16)) },\n    };\n    this.addExternalEntities = addExternalEntities;\n    this.parseXml = parseXml;\n    this.parseTextData = parseTextData;\n    this.resolveNameSpace = resolveNameSpace;\n    this.buildAttributesMap = buildAttributesMap;\n    this.isItStopNode = isItStopNode;\n    this.replaceEntitiesValue = replaceEntitiesValue;\n    this.readStopNodeData = readStopNodeData;\n    this.saveTextToParentTag = saveTextToParentTag;\n    this.addChild = addChild;\n    this.ignoreAttributesFn = getIgnoreAttributesFn(this.options.ignoreAttributes)\n  }\n\n}\n\nfunction addExternalEntities(externalEntities){\n  const entKeys = Object.keys(externalEntities);\n  for (let i = 0; i < entKeys.length; i++) {\n    const ent = entKeys[i];\n    this.lastEntities[ent] = {\n       regex: new RegExp(\"&\"+ent+\";\",\"g\"),\n       val : externalEntities[ent]\n    }\n  }\n}\n\n/**\n * @param {string} val\n * @param {string} tagName\n * @param {string} jPath\n * @param {boolean} dontTrim\n * @param {boolean} hasAttributes\n * @param {boolean} isLeafNode\n * @param {boolean} escapeEntities\n */\nfunction parseTextData(val, tagName, jPath, dontTrim, hasAttributes, isLeafNode, escapeEntities) {\n  if (val !== undefined) {\n    if (this.options.trimValues && !dontTrim) {\n      val = val.trim();\n    }\n    if(val.length > 0){\n      if(!escapeEntities) val = this.replaceEntitiesValue(val);\n      \n      const newval = this.options.tagValueProcessor(tagName, val, jPath, hasAttributes, isLeafNode);\n      if(newval === null || newval === undefined){\n        //don't parse\n        return val;\n      }else if(typeof newval !== typeof val || newval !== val){\n        //overwrite\n        return newval;\n      }else if(this.options.trimValues){\n        return parseValue(val, this.options.parseTagValue, this.options.numberParseOptions);\n      }else{\n        const trimmedVal = val.trim();\n        if(trimmedVal === val){\n          return parseValue(val, this.options.parseTagValue, this.options.numberParseOptions);\n        }else{\n          return val;\n        }\n      }\n    }\n  }\n}\n\nfunction resolveNameSpace(tagname) {\n  if (this.options.removeNSPrefix) {\n    const tags = tagname.split(':');\n    const prefix = tagname.charAt(0) === '/' ? '/' : '';\n    if (tags[0] === 'xmlns') {\n      return '';\n    }\n    if (tags.length === 2) {\n      tagname = prefix + tags[1];\n    }\n  }\n  return tagname;\n}\n\n//TODO: change regex to capture NS\n//const attrsRegx = new RegExp(\"([\\\\w\\\\-\\\\.\\\\:]+)\\\\s*=\\\\s*(['\\\"])((.|\\n)*?)\\\\2\",\"gm\");\nconst attrsRegx = new RegExp('([^\\\\s=]+)\\\\s*(=\\\\s*([\\'\"])([\\\\s\\\\S]*?)\\\\3)?', 'gm');\n\nfunction buildAttributesMap(attrStr, jPath, tagName) {\n  if (this.options.ignoreAttributes !== true && typeof attrStr === 'string') {\n    // attrStr = attrStr.replace(/\\r?\\n/g, ' ');\n    //attrStr = attrStr || attrStr.trim();\n\n    const matches = getAllMatches(attrStr, attrsRegx);\n    const len = matches.length; //don't make it inline\n    const attrs = {};\n    for (let i = 0; i < len; i++) {\n      const attrName = this.resolveNameSpace(matches[i][1]);\n      if (this.ignoreAttributesFn(attrName, jPath)) {\n        continue\n      }\n      let oldVal = matches[i][4];\n      let aName = this.options.attributeNamePrefix + attrName;\n      if (attrName.length) {\n        if (this.options.transformAttributeName) {\n          aName = this.options.transformAttributeName(aName);\n        }\n        if(aName === \"__proto__\") aName  = \"#__proto__\";\n        if (oldVal !== undefined) {\n          if (this.options.trimValues) {\n            oldVal = oldVal.trim();\n          }\n          oldVal = this.replaceEntitiesValue(oldVal);\n          const newVal = this.options.attributeValueProcessor(attrName, oldVal, jPath);\n          if(newVal === null || newVal === undefined){\n            //don't parse\n            attrs[aName] = oldVal;\n          }else if(typeof newVal !== typeof oldVal || newVal !== oldVal){\n            //overwrite\n            attrs[aName] = newVal;\n          }else{\n            //parse\n            attrs[aName] = parseValue(\n              oldVal,\n              this.options.parseAttributeValue,\n              this.options.numberParseOptions\n            );\n          }\n        } else if (this.options.allowBooleanAttributes) {\n          attrs[aName] = true;\n        }\n      }\n    }\n    if (!Object.keys(attrs).length) {\n      return;\n    }\n    if (this.options.attributesGroupName) {\n      const attrCollection = {};\n      attrCollection[this.options.attributesGroupName] = attrs;\n      return attrCollection;\n    }\n    return attrs\n  }\n}\n\nconst parseXml = function(xmlData) {\n  xmlData = xmlData.replace(/\\r\\n?/g, \"\\n\"); //TODO: remove this line\n  const xmlObj = new xmlNode('!xml');\n  let currentNode = xmlObj;\n  let textData = \"\";\n  let jPath = \"\";\n  for(let i=0; i< xmlData.length; i++){//for each char in XML data\n    const ch = xmlData[i];\n    if(ch === '<'){\n      // const nextIndex = i+1;\n      // const _2ndChar = xmlData[nextIndex];\n      if( xmlData[i+1] === '/') {//Closing Tag\n        const closeIndex = findClosingIndex(xmlData, \">\", i, \"Closing Tag is not closed.\")\n        let tagName = xmlData.substring(i+2,closeIndex).trim();\n\n        if(this.options.removeNSPrefix){\n          const colonIndex = tagName.indexOf(\":\");\n          if(colonIndex !== -1){\n            tagName = tagName.substr(colonIndex+1);\n          }\n        }\n\n        if(this.options.transformTagName) {\n          tagName = this.options.transformTagName(tagName);\n        }\n\n        if(currentNode){\n          textData = this.saveTextToParentTag(textData, currentNode, jPath);\n        }\n\n        //check if last tag of nested tag was unpaired tag\n        const lastTagName = jPath.substring(jPath.lastIndexOf(\".\")+1);\n        if(tagName && this.options.unpairedTags.indexOf(tagName) !== -1 ){\n          throw new Error(`Unpaired tag can not be used as closing tag: </${tagName}>`);\n        }\n        let propIndex = 0\n        if(lastTagName && this.options.unpairedTags.indexOf(lastTagName) !== -1 ){\n          propIndex = jPath.lastIndexOf('.', jPath.lastIndexOf('.')-1)\n          this.tagsNodeStack.pop();\n        }else{\n          propIndex = jPath.lastIndexOf(\".\");\n        }\n        jPath = jPath.substring(0, propIndex);\n\n        currentNode = this.tagsNodeStack.pop();//avoid recursion, set the parent tag scope\n        textData = \"\";\n        i = closeIndex;\n      } else if( xmlData[i+1] === '?') {\n\n        let tagData = readTagExp(xmlData,i, false, \"?>\");\n        if(!tagData) throw new Error(\"Pi Tag is not closed.\");\n\n        textData = this.saveTextToParentTag(textData, currentNode, jPath);\n        if( (this.options.ignoreDeclaration && tagData.tagName === \"?xml\") || this.options.ignorePiTags){\n\n        }else{\n  \n          const childNode = new xmlNode(tagData.tagName);\n          childNode.add(this.options.textNodeName, \"\");\n          \n          if(tagData.tagName !== tagData.tagExp && tagData.attrExpPresent){\n            childNode[\":@\"] = this.buildAttributesMap(tagData.tagExp, jPath, tagData.tagName);\n          }\n          this.addChild(currentNode, childNode, jPath, i);\n        }\n\n\n        i = tagData.closeIndex + 1;\n      } else if(xmlData.substr(i + 1, 3) === '!--') {\n        const endIndex = findClosingIndex(xmlData, \"-->\", i+4, \"Comment is not closed.\")\n        if(this.options.commentPropName){\n          const comment = xmlData.substring(i + 4, endIndex - 2);\n\n          textData = this.saveTextToParentTag(textData, currentNode, jPath);\n\n          currentNode.add(this.options.commentPropName, [ { [this.options.textNodeName] : comment } ]);\n        }\n        i = endIndex;\n      } else if( xmlData.substr(i + 1, 2) === '!D') {\n        const result = readDocType(xmlData, i);\n        this.docTypeEntities = result.entities;\n        i = result.i;\n      }else if(xmlData.substr(i + 1, 2) === '![') {\n        const closeIndex = findClosingIndex(xmlData, \"]]>\", i, \"CDATA is not closed.\") - 2;\n        const tagExp = xmlData.substring(i + 9,closeIndex);\n\n        textData = this.saveTextToParentTag(textData, currentNode, jPath);\n\n        let val = this.parseTextData(tagExp, currentNode.tagname, jPath, true, false, true, true);\n        if(val == undefined) val = \"\";\n\n        //cdata should be set even if it is 0 length string\n        if(this.options.cdataPropName){\n          currentNode.add(this.options.cdataPropName, [ { [this.options.textNodeName] : tagExp } ]);\n        }else{\n          currentNode.add(this.options.textNodeName, val);\n        }\n        \n        i = closeIndex + 2;\n      }else {//Opening tag\n        let result = readTagExp(xmlData,i, this.options.removeNSPrefix);\n        let tagName= result.tagName;\n        const rawTagName = result.rawTagName;\n        let tagExp = result.tagExp;\n        let attrExpPresent = result.attrExpPresent;\n        let closeIndex = result.closeIndex;\n\n        if (this.options.transformTagName) {\n          tagName = this.options.transformTagName(tagName);\n        }\n        \n        //save text as child node\n        if (currentNode && textData) {\n          if(currentNode.tagname !== '!xml'){\n            //when nested tag is found\n            textData = this.saveTextToParentTag(textData, currentNode, jPath, false);\n          }\n        }\n\n        //check if last tag was unpaired tag\n        const lastTag = currentNode;\n        if(lastTag && this.options.unpairedTags.indexOf(lastTag.tagname) !== -1 ){\n          currentNode = this.tagsNodeStack.pop();\n          jPath = jPath.substring(0, jPath.lastIndexOf(\".\"));\n        }\n        if(tagName !== xmlObj.tagname){\n          jPath += jPath ? \".\" + tagName : tagName;\n        }\n        const startIndex = i;\n        if (this.isItStopNode(this.options.stopNodes, jPath, tagName)) {\n          let tagContent = \"\";\n          //self-closing tag\n          if(tagExp.length > 0 && tagExp.lastIndexOf(\"/\") === tagExp.length - 1){\n            if(tagName[tagName.length - 1] === \"/\"){ //remove trailing '/'\n              tagName = tagName.substr(0, tagName.length - 1);\n              jPath = jPath.substr(0, jPath.length - 1);\n              tagExp = tagName;\n            }else{\n              tagExp = tagExp.substr(0, tagExp.length - 1);\n            }\n            i = result.closeIndex;\n          }\n          //unpaired tag\n          else if(this.options.unpairedTags.indexOf(tagName) !== -1){\n            \n            i = result.closeIndex;\n          }\n          //normal tag\n          else{\n            //read until closing tag is found\n            const result = this.readStopNodeData(xmlData, rawTagName, closeIndex + 1);\n            if(!result) throw new Error(`Unexpected end of ${rawTagName}`);\n            i = result.i;\n            tagContent = result.tagContent;\n          }\n\n          const childNode = new xmlNode(tagName);\n\n          if(tagName !== tagExp && attrExpPresent){\n            childNode[\":@\"] = this.buildAttributesMap(tagExp, jPath, tagName);\n          }\n          if(tagContent) {\n            tagContent = this.parseTextData(tagContent, tagName, jPath, true, attrExpPresent, true, true);\n          }\n          \n          jPath = jPath.substr(0, jPath.lastIndexOf(\".\"));\n          childNode.add(this.options.textNodeName, tagContent);\n          \n          this.addChild(currentNode, childNode, jPath, startIndex);\n        }else{\n  //selfClosing tag\n          if(tagExp.length > 0 && tagExp.lastIndexOf(\"/\") === tagExp.length - 1){\n            if(tagName[tagName.length - 1] === \"/\"){ //remove trailing '/'\n              tagName = tagName.substr(0, tagName.length - 1);\n              jPath = jPath.substr(0, jPath.length - 1);\n              tagExp = tagName;\n            }else{\n              tagExp = tagExp.substr(0, tagExp.length - 1);\n            }\n            \n            if(this.options.transformTagName) {\n              tagName = this.options.transformTagName(tagName);\n            }\n\n            const childNode = new xmlNode(tagName);\n            if(tagName !== tagExp && attrExpPresent){\n              childNode[\":@\"] = this.buildAttributesMap(tagExp, jPath, tagName);\n            }\n            this.addChild(currentNode, childNode, jPath, startIndex);\n            jPath = jPath.substr(0, jPath.lastIndexOf(\".\"));\n          }\n    //opening tag\n          else{\n            const childNode = new xmlNode( tagName);\n            this.tagsNodeStack.push(currentNode);\n            \n            if(tagName !== tagExp && attrExpPresent){\n              childNode[\":@\"] = this.buildAttributesMap(tagExp, jPath, tagName);\n            }\n            this.addChild(currentNode, childNode, jPath, startIndex);\n            currentNode = childNode;\n          }\n          textData = \"\";\n          i = closeIndex;\n        }\n      }\n    }else{\n      textData += xmlData[i];\n    }\n  }\n  return xmlObj.child;\n}\n\nfunction addChild(currentNode, childNode, jPath, startIndex){\n  // unset startIndex if not requested\n  if (!this.options.captureMetaData) startIndex = undefined;\n  const result = this.options.updateTag(childNode.tagname, jPath, childNode[\":@\"])\n  if(result === false){\n  } else if(typeof result === \"string\"){\n    childNode.tagname = result\n    currentNode.addChild(childNode, startIndex);\n  }else{\n    currentNode.addChild(childNode, startIndex);\n  }\n}\n\nconst replaceEntitiesValue = function(val){\n\n  if(this.options.processEntities){\n    for(let entityName in this.docTypeEntities){\n      const entity = this.docTypeEntities[entityName];\n      val = val.replace( entity.regx, entity.val);\n    }\n    for(let entityName in this.lastEntities){\n      const entity = this.lastEntities[entityName];\n      val = val.replace( entity.regex, entity.val);\n    }\n    if(this.options.htmlEntities){\n      for(let entityName in this.htmlEntities){\n        const entity = this.htmlEntities[entityName];\n        val = val.replace( entity.regex, entity.val);\n      }\n    }\n    val = val.replace( this.ampEntity.regex, this.ampEntity.val);\n  }\n  return val;\n}\nfunction saveTextToParentTag(textData, currentNode, jPath, isLeafNode) {\n  if (textData) { //store previously collected data as textNode\n    if(isLeafNode === undefined) isLeafNode = currentNode.child.length === 0\n    \n    textData = this.parseTextData(textData,\n      currentNode.tagname,\n      jPath,\n      false,\n      currentNode[\":@\"] ? Object.keys(currentNode[\":@\"]).length !== 0 : false,\n      isLeafNode);\n\n    if (textData !== undefined && textData !== \"\")\n      currentNode.add(this.options.textNodeName, textData);\n    textData = \"\";\n  }\n  return textData;\n}\n\n//TODO: use jPath to simplify the logic\n/**\n * \n * @param {string[]} stopNodes \n * @param {string} jPath\n * @param {string} currentTagName \n */\nfunction isItStopNode(stopNodes, jPath, currentTagName){\n  const allNodesExp = \"*.\" + currentTagName;\n  for (const stopNodePath in stopNodes) {\n    const stopNodeExp = stopNodes[stopNodePath];\n    if( allNodesExp === stopNodeExp || jPath === stopNodeExp  ) return true;\n  }\n  return false;\n}\n\n/**\n * Returns the tag Expression and where it is ending handling single-double quotes situation\n * @param {string} xmlData \n * @param {number} i starting index\n * @returns \n */\nfunction tagExpWithClosingIndex(xmlData, i, closingChar = \">\"){\n  let attrBoundary;\n  let tagExp = \"\";\n  for (let index = i; index < xmlData.length; index++) {\n    let ch = xmlData[index];\n    if (attrBoundary) {\n        if (ch === attrBoundary) attrBoundary = \"\";//reset\n    } else if (ch === '\"' || ch === \"'\") {\n        attrBoundary = ch;\n    } else if (ch === closingChar[0]) {\n      if(closingChar[1]){\n        if(xmlData[index + 1] === closingChar[1]){\n          return {\n            data: tagExp,\n            index: index\n          }\n        }\n      }else{\n        return {\n          data: tagExp,\n          index: index\n        }\n      }\n    } else if (ch === '\\t') {\n      ch = \" \"\n    }\n    tagExp += ch;\n  }\n}\n\nfunction findClosingIndex(xmlData, str, i, errMsg){\n  const closingIndex = xmlData.indexOf(str, i);\n  if(closingIndex === -1){\n    throw new Error(errMsg)\n  }else{\n    return closingIndex + str.length - 1;\n  }\n}\n\nfunction readTagExp(xmlData,i, removeNSPrefix, closingChar = \">\"){\n  const result = tagExpWithClosingIndex(xmlData, i+1, closingChar);\n  if(!result) return;\n  let tagExp = result.data;\n  const closeIndex = result.index;\n  const separatorIndex = tagExp.search(/\\s/);\n  let tagName = tagExp;\n  let attrExpPresent = true;\n  if(separatorIndex !== -1){//separate tag name and attributes expression\n    tagName = tagExp.substring(0, separatorIndex);\n    tagExp = tagExp.substring(separatorIndex + 1).trimStart();\n  }\n\n  const rawTagName = tagName;\n  if(removeNSPrefix){\n    const colonIndex = tagName.indexOf(\":\");\n    if(colonIndex !== -1){\n      tagName = tagName.substr(colonIndex+1);\n      attrExpPresent = tagName !== result.data.substr(colonIndex + 1);\n    }\n  }\n\n  return {\n    tagName: tagName,\n    tagExp: tagExp,\n    closeIndex: closeIndex,\n    attrExpPresent: attrExpPresent,\n    rawTagName: rawTagName,\n  }\n}\n/**\n * find paired tag for a stop node\n * @param {string} xmlData \n * @param {string} tagName \n * @param {number} i \n */\nfunction readStopNodeData(xmlData, tagName, i){\n  const startIndex = i;\n  // Starting at 1 since we already have an open tag\n  let openTagCount = 1;\n\n  for (; i < xmlData.length; i++) {\n    if( xmlData[i] === \"<\"){ \n      if (xmlData[i+1] === \"/\") {//close tag\n          const closeIndex = findClosingIndex(xmlData, \">\", i, `${tagName} is not closed`);\n          let closeTagName = xmlData.substring(i+2,closeIndex).trim();\n          if(closeTagName === tagName){\n            openTagCount--;\n            if (openTagCount === 0) {\n              return {\n                tagContent: xmlData.substring(startIndex, i),\n                i : closeIndex\n              }\n            }\n          }\n          i=closeIndex;\n        } else if(xmlData[i+1] === '?') { \n          const closeIndex = findClosingIndex(xmlData, \"?>\", i+1, \"StopNode is not closed.\")\n          i=closeIndex;\n        } else if(xmlData.substr(i + 1, 3) === '!--') { \n          const closeIndex = findClosingIndex(xmlData, \"-->\", i+3, \"StopNode is not closed.\")\n          i=closeIndex;\n        } else if(xmlData.substr(i + 1, 2) === '![') { \n          const closeIndex = findClosingIndex(xmlData, \"]]>\", i, \"StopNode is not closed.\") - 2;\n          i=closeIndex;\n        } else {\n          const tagData = readTagExp(xmlData, i, '>')\n\n          if (tagData) {\n            const openTagName = tagData && tagData.tagName;\n            if (openTagName === tagName && tagData.tagExp[tagData.tagExp.length-1] !== \"/\") {\n              openTagCount++;\n            }\n            i=tagData.closeIndex;\n          }\n        }\n      }\n  }//end for loop\n}\n\nfunction parseValue(val, shouldParse, options) {\n  if (shouldParse && typeof val === 'string') {\n    //console.log(options)\n    const newval = val.trim();\n    if(newval === 'true' ) return true;\n    else if(newval === 'false' ) return false;\n    else return toNumber(val, options);\n  } else {\n    if (isExist(val)) {\n      return val;\n    } else {\n      return '';\n    }\n  }\n}\n", "'use strict';\n\nimport XmlNode from './xmlNode.js';\n\nconst METADATA_SYMBOL = XmlNode.getMetaDataSymbol();\n\n/**\n * \n * @param {array} node \n * @param {any} options \n * @returns \n */\nexport default function prettify(node, options){\n  return compress( node, options);\n}\n\n/**\n * \n * @param {array} arr \n * @param {object} options \n * @param {string} jPath \n * @returns object\n */\nfunction compress(arr, options, jPath){\n  let text;\n  const compressedObj = {};\n  for (let i = 0; i < arr.length; i++) {\n    const tagObj = arr[i];\n    const property = propName(tagObj);\n    let newJpath = \"\";\n    if(jPath === undefined) newJpath = property;\n    else newJpath = jPath + \".\" + property;\n\n    if(property === options.textNodeName){\n      if(text === undefined) text = tagObj[property];\n      else text += \"\" + tagObj[property];\n    }else if(property === undefined){\n      continue;\n    }else if(tagObj[property]){\n      \n      let val = compress(tagObj[property], options, newJpath);\n      const isLeaf = isLeafTag(val, options);\n      if (tagObj[METADATA_SYMBOL] !== undefined) {\n        val[METADATA_SYMBOL] = tagObj[METADATA_SYMBOL]; // copy over metadata\n      }\n\n      if(tagObj[\":@\"]){\n        assignAttributes( val, tagObj[\":@\"], newJpath, options);\n      }else if(Object.keys(val).length === 1 && val[options.textNodeName] !== undefined && !options.alwaysCreateTextNode){\n        val = val[options.textNodeName];\n      }else if(Object.keys(val).length === 0){\n        if(options.alwaysCreateTextNode) val[options.textNodeName] = \"\";\n        else val = \"\";\n      }\n\n      if(compressedObj[property] !== undefined && compressedObj.hasOwnProperty(property)) {\n        if(!Array.isArray(compressedObj[property])) {\n            compressedObj[property] = [ compressedObj[property] ];\n        }\n        compressedObj[property].push(val);\n      }else{\n        //TODO: if a node is not an array, then check if it should be an array\n        //also determine if it is a leaf node\n        if (options.isArray(property, newJpath, isLeaf )) {\n          compressedObj[property] = [val];\n        }else{\n          compressedObj[property] = val;\n        }\n      }\n    }\n    \n  }\n  // if(text && text.length > 0) compressedObj[options.textNodeName] = text;\n  if(typeof text === \"string\"){\n    if(text.length > 0) compressedObj[options.textNodeName] = text;\n  }else if(text !== undefined) compressedObj[options.textNodeName] = text;\n  return compressedObj;\n}\n\nfunction propName(obj){\n  const keys = Object.keys(obj);\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    if(key !== \":@\") return key;\n  }\n}\n\nfunction assignAttributes(obj, attrMap, jpath, options){\n  if (attrMap) {\n    const keys = Object.keys(attrMap);\n    const len = keys.length; //don't make it inline\n    for (let i = 0; i < len; i++) {\n      const atrrName = keys[i];\n      if (options.isArray(atrrName, jpath + \".\" + atrrName, true, true)) {\n        obj[atrrName] = [ attrMap[atrrName] ];\n      } else {\n        obj[atrrName] = attrMap[atrrName];\n      }\n    }\n  }\n}\n\nfunction isLeafTag(obj, options){\n  const { textNodeName } = options;\n  const propCount = Object.keys(obj).length;\n  \n  if (propCount === 0) {\n    return true;\n  }\n\n  if (\n    propCount === 1 &&\n    (obj[textNodeName] || typeof obj[textNodeName] === \"boolean\" || obj[textNodeName] === 0)\n  ) {\n    return true;\n  }\n\n  return false;\n}\n", "import { buildOptions} from './OptionsBuilder.js';\nimport OrderedObjParser from './OrderedObjParser.js';\nimport prettify from './node2json.js';\nimport {validate} from \"../validator.js\";\nimport XmlNode from './xmlNode.js';\n\nexport default class XMLParser{\n    \n    constructor(options){\n        this.externalEntities = {};\n        this.options = buildOptions(options);\n        \n    }\n    /**\n     * Parse XML dats to JS object \n     * @param {string|Buffer} xmlData \n     * @param {boolean|Object} validationOption \n     */\n    parse(xmlData,validationOption){\n        if(typeof xmlData === \"string\"){\n        }else if( xmlData.toString){\n            xmlData = xmlData.toString();\n        }else{\n            throw new Error(\"XML data is accepted in String or Bytes[] form.\")\n        }\n        if( validationOption){\n            if(validationOption === true) validationOption = {}; //validate with default options\n            \n            const result = validate(xmlData, validationOption);\n            if (result !== true) {\n              throw Error( `${result.err.msg}:${result.err.line}:${result.err.col}` )\n            }\n          }\n        const orderedObjParser = new OrderedObjParser(this.options);\n        orderedObjParser.addExternalEntities(this.externalEntities);\n        const orderedResult = orderedObjParser.parseXml(xmlData);\n        if(this.options.preserveOrder || orderedResult === undefined) return orderedResult;\n        else return prettify(orderedResult, this.options);\n    }\n\n    /**\n     * Add Entity which is not by default supported by this library\n     * @param {string} key \n     * @param {string} value \n     */\n    addEntity(key, value){\n        if(value.indexOf(\"&\") !== -1){\n            throw new Error(\"Entity value can't have '&'\")\n        }else if(key.indexOf(\"&\") !== -1 || key.indexOf(\";\") !== -1){\n            throw new Error(\"An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'\")\n        }else if(value === \"&\"){\n            throw new Error(\"An entity with value '&' is not permitted\");\n        }else{\n            this.externalEntities[key] = value;\n        }\n    }\n\n    /**\n     * Returns a Symbol that can be used to access the metadata\n     * property on a node.\n     * \n     * If Symbol is not available in the environment, an ordinary property is used\n     * and the name of the property is here returned.\n     * \n     * The XMLMetaData property is only present when `captureMetaData`\n     * is true in the options.\n     */\n    static getMetaDataSymbol() {\n        return XmlNode.getMetaDataSymbol();\n    }\n}\n", "const EOL = \"\\n\";\n\n/**\n * \n * @param {array} jArray \n * @param {any} options \n * @returns \n */\nexport default function toXml(jArray, options) {\n    let indentation = \"\";\n    if (options.format && options.indentBy.length > 0) {\n        indentation = EOL;\n    }\n    return arrToStr(jArray, options, \"\", indentation);\n}\n\nfunction arrToStr(arr, options, jPath, indentation) {\n    let xmlStr = \"\";\n    let isPreviousElementTag = false;\n\n    for (let i = 0; i < arr.length; i++) {\n        const tagObj = arr[i];\n        const tagName = propName(tagObj);\n        if(tagName === undefined) continue;\n\n        let newJPath = \"\";\n        if (jPath.length === 0) newJPath = tagName\n        else newJPath = `${jPath}.${tagName}`;\n\n        if (tagName === options.textNodeName) {\n            let tagText = tagObj[tagName];\n            if (!isStopNode(newJPath, options)) {\n                tagText = options.tagValueProcessor(tagName, tagText);\n                tagText = replaceEntitiesValue(tagText, options);\n            }\n            if (isPreviousElementTag) {\n                xmlStr += indentation;\n            }\n            xmlStr += tagText;\n            isPreviousElementTag = false;\n            continue;\n        } else if (tagName === options.cdataPropName) {\n            if (isPreviousElementTag) {\n                xmlStr += indentation;\n            }\n            xmlStr += `<![CDATA[${tagObj[tagName][0][options.textNodeName]}]]>`;\n            isPreviousElementTag = false;\n            continue;\n        } else if (tagName === options.commentPropName) {\n            xmlStr += indentation + `<!--${tagObj[tagName][0][options.textNodeName]}-->`;\n            isPreviousElementTag = true;\n            continue;\n        } else if (tagName[0] === \"?\") {\n            const attStr = attr_to_str(tagObj[\":@\"], options);\n            const tempInd = tagName === \"?xml\" ? \"\" : indentation;\n            let piTextNodeName = tagObj[tagName][0][options.textNodeName];\n            piTextNodeName = piTextNodeName.length !== 0 ? \" \" + piTextNodeName : \"\"; //remove extra spacing\n            xmlStr += tempInd + `<${tagName}${piTextNodeName}${attStr}?>`;\n            isPreviousElementTag = true;\n            continue;\n        }\n        let newIdentation = indentation;\n        if (newIdentation !== \"\") {\n            newIdentation += options.indentBy;\n        }\n        const attStr = attr_to_str(tagObj[\":@\"], options);\n        const tagStart = indentation + `<${tagName}${attStr}`;\n        const tagValue = arrToStr(tagObj[tagName], options, newJPath, newIdentation);\n        if (options.unpairedTags.indexOf(tagName) !== -1) {\n            if (options.suppressUnpairedNode) xmlStr += tagStart + \">\";\n            else xmlStr += tagStart + \"/>\";\n        } else if ((!tagValue || tagValue.length === 0) && options.suppressEmptyNode) {\n            xmlStr += tagStart + \"/>\";\n        } else if (tagValue && tagValue.endsWith(\">\")) {\n            xmlStr += tagStart + `>${tagValue}${indentation}</${tagName}>`;\n        } else {\n            xmlStr += tagStart + \">\";\n            if (tagValue && indentation !== \"\" && (tagValue.includes(\"/>\") || tagValue.includes(\"</\"))) {\n                xmlStr += indentation + options.indentBy + tagValue + indentation;\n            } else {\n                xmlStr += tagValue;\n            }\n            xmlStr += `</${tagName}>`;\n        }\n        isPreviousElementTag = true;\n    }\n\n    return xmlStr;\n}\n\nfunction propName(obj) {\n    const keys = Object.keys(obj);\n    for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        if(!obj.hasOwnProperty(key)) continue;\n        if (key !== \":@\") return key;\n    }\n}\n\nfunction attr_to_str(attrMap, options) {\n    let attrStr = \"\";\n    if (attrMap && !options.ignoreAttributes) {\n        for (let attr in attrMap) {\n            if(!attrMap.hasOwnProperty(attr)) continue;\n            let attrVal = options.attributeValueProcessor(attr, attrMap[attr]);\n            attrVal = replaceEntitiesValue(attrVal, options);\n            if (attrVal === true && options.suppressBooleanAttributes) {\n                attrStr += ` ${attr.substr(options.attributeNamePrefix.length)}`;\n            } else {\n                attrStr += ` ${attr.substr(options.attributeNamePrefix.length)}=\"${attrVal}\"`;\n            }\n        }\n    }\n    return attrStr;\n}\n\nfunction isStopNode(jPath, options) {\n    jPath = jPath.substr(0, jPath.length - options.textNodeName.length - 1);\n    let tagName = jPath.substr(jPath.lastIndexOf(\".\") + 1);\n    for (let index in options.stopNodes) {\n        if (options.stopNodes[index] === jPath || options.stopNodes[index] === \"*.\" + tagName) return true;\n    }\n    return false;\n}\n\nfunction replaceEntitiesValue(textValue, options) {\n    if (textValue && textValue.length > 0 && options.processEntities) {\n        for (let i = 0; i < options.entities.length; i++) {\n            const entity = options.entities[i];\n            textValue = textValue.replace(entity.regex, entity.val);\n        }\n    }\n    return textValue;\n}\n", "'use strict';\n//parse Empty Node as self closing node\nimport buildFromOrderedJs from './orderedJs2Xml.js';\nimport getIgnoreAttributesFn from \"../ignoreAttributes.js\";\n\nconst defaultOptions = {\n  attributeNamePrefix: '@_',\n  attributesGroupName: false,\n  textNodeName: '#text',\n  ignoreAttributes: true,\n  cdataPropName: false,\n  format: false,\n  indentBy: '  ',\n  suppressEmptyNode: false,\n  suppressUnpairedNode: true,\n  suppressBooleanAttributes: true,\n  tagValueProcessor: function(key, a) {\n    return a;\n  },\n  attributeValueProcessor: function(attrName, a) {\n    return a;\n  },\n  preserveOrder: false,\n  commentPropName: false,\n  unpairedTags: [],\n  entities: [\n    { regex: new RegExp(\"&\", \"g\"), val: \"&amp;\" },//it must be on top\n    { regex: new RegExp(\">\", \"g\"), val: \"&gt;\" },\n    { regex: new RegExp(\"<\", \"g\"), val: \"&lt;\" },\n    { regex: new RegExp(\"\\'\", \"g\"), val: \"&apos;\" },\n    { regex: new RegExp(\"\\\"\", \"g\"), val: \"&quot;\" }\n  ],\n  processEntities: true,\n  stopNodes: [],\n  // transformTagName: false,\n  // transformAttributeName: false,\n  oneListGroup: false\n};\n\nexport default function Builder(options) {\n  this.options = Object.assign({}, defaultOptions, options);\n  if (this.options.ignoreAttributes === true || this.options.attributesGroupName) {\n    this.isAttribute = function(/*a*/) {\n      return false;\n    };\n  } else {\n    this.ignoreAttributesFn = getIgnoreAttributesFn(this.options.ignoreAttributes)\n    this.attrPrefixLen = this.options.attributeNamePrefix.length;\n    this.isAttribute = isAttribute;\n  }\n\n  this.processTextOrObjNode = processTextOrObjNode\n\n  if (this.options.format) {\n    this.indentate = indentate;\n    this.tagEndChar = '>\\n';\n    this.newLine = '\\n';\n  } else {\n    this.indentate = function() {\n      return '';\n    };\n    this.tagEndChar = '>';\n    this.newLine = '';\n  }\n}\n\nBuilder.prototype.build = function(jObj) {\n  if(this.options.preserveOrder){\n    return buildFromOrderedJs(jObj, this.options);\n  }else {\n    if(Array.isArray(jObj) && this.options.arrayNodeName && this.options.arrayNodeName.length > 1){\n      jObj = {\n        [this.options.arrayNodeName] : jObj\n      }\n    }\n    return this.j2x(jObj, 0, []).val;\n  }\n};\n\nBuilder.prototype.j2x = function(jObj, level, ajPath) {\n  let attrStr = '';\n  let val = '';\n  const jPath = ajPath.join('.')\n  for (let key in jObj) {\n    if(!Object.prototype.hasOwnProperty.call(jObj, key)) continue;\n    if (typeof jObj[key] === 'undefined') {\n      // supress undefined node only if it is not an attribute\n      if (this.isAttribute(key)) {\n        val += '';\n      }\n    } else if (jObj[key] === null) {\n      // null attribute should be ignored by the attribute list, but should not cause the tag closing\n      if (this.isAttribute(key)) {\n        val += '';\n      } else if (key === this.options.cdataPropName) {\n        val += '';\n      } else if (key[0] === '?') {\n        val += this.indentate(level) + '<' + key + '?' + this.tagEndChar;\n      } else {\n        val += this.indentate(level) + '<' + key + '/' + this.tagEndChar;\n      }\n      // val += this.indentate(level) + '<' + key + '/' + this.tagEndChar;\n    } else if (jObj[key] instanceof Date) {\n      val += this.buildTextValNode(jObj[key], key, '', level);\n    } else if (typeof jObj[key] !== 'object') {\n      //premitive type\n      const attr = this.isAttribute(key);\n      if (attr && !this.ignoreAttributesFn(attr, jPath)) {\n        attrStr += this.buildAttrPairStr(attr, '' + jObj[key]);\n      } else if (!attr) {\n        //tag value\n        if (key === this.options.textNodeName) {\n          let newval = this.options.tagValueProcessor(key, '' + jObj[key]);\n          val += this.replaceEntitiesValue(newval);\n        } else {\n          val += this.buildTextValNode(jObj[key], key, '', level);\n        }\n      }\n    } else if (Array.isArray(jObj[key])) {\n      //repeated nodes\n      const arrLen = jObj[key].length;\n      let listTagVal = \"\";\n      let listTagAttr = \"\";\n      for (let j = 0; j < arrLen; j++) {\n        const item = jObj[key][j];\n        if (typeof item === 'undefined') {\n          // supress undefined node\n        } else if (item === null) {\n          if(key[0] === \"?\") val += this.indentate(level) + '<' + key + '?' + this.tagEndChar;\n          else val += this.indentate(level) + '<' + key + '/' + this.tagEndChar;\n          // val += this.indentate(level) + '<' + key + '/' + this.tagEndChar;\n        } else if (typeof item === 'object') {\n          if(this.options.oneListGroup){\n            const result = this.j2x(item, level + 1, ajPath.concat(key));\n            listTagVal += result.val;\n            if (this.options.attributesGroupName && item.hasOwnProperty(this.options.attributesGroupName)) {\n              listTagAttr += result.attrStr\n            }\n          }else{\n            listTagVal += this.processTextOrObjNode(item, key, level, ajPath)\n          }\n        } else {\n          if (this.options.oneListGroup) {\n            let textValue = this.options.tagValueProcessor(key, item);\n            textValue = this.replaceEntitiesValue(textValue);\n            listTagVal += textValue;\n          } else {\n            listTagVal += this.buildTextValNode(item, key, '', level);\n          }\n        }\n      }\n      if(this.options.oneListGroup){\n        listTagVal = this.buildObjectNode(listTagVal, key, listTagAttr, level);\n      }\n      val += listTagVal;\n    } else {\n      //nested node\n      if (this.options.attributesGroupName && key === this.options.attributesGroupName) {\n        const Ks = Object.keys(jObj[key]);\n        const L = Ks.length;\n        for (let j = 0; j < L; j++) {\n          attrStr += this.buildAttrPairStr(Ks[j], '' + jObj[key][Ks[j]]);\n        }\n      } else {\n        val += this.processTextOrObjNode(jObj[key], key, level, ajPath)\n      }\n    }\n  }\n  return {attrStr: attrStr, val: val};\n};\n\nBuilder.prototype.buildAttrPairStr = function(attrName, val){\n  val = this.options.attributeValueProcessor(attrName, '' + val);\n  val = this.replaceEntitiesValue(val);\n  if (this.options.suppressBooleanAttributes && val === \"true\") {\n    return ' ' + attrName;\n  } else return ' ' + attrName + '=\"' + val + '\"';\n}\n\nfunction processTextOrObjNode (object, key, level, ajPath) {\n  const result = this.j2x(object, level + 1, ajPath.concat(key));\n  if (object[this.options.textNodeName] !== undefined && Object.keys(object).length === 1) {\n    return this.buildTextValNode(object[this.options.textNodeName], key, result.attrStr, level);\n  } else {\n    return this.buildObjectNode(result.val, key, result.attrStr, level);\n  }\n}\n\nBuilder.prototype.buildObjectNode = function(val, key, attrStr, level) {\n  if(val === \"\"){\n    if(key[0] === \"?\") return  this.indentate(level) + '<' + key + attrStr+ '?' + this.tagEndChar;\n    else {\n      return this.indentate(level) + '<' + key + attrStr + this.closeTag(key) + this.tagEndChar;\n    }\n  }else{\n\n    let tagEndExp = '</' + key + this.tagEndChar;\n    let piClosingChar = \"\";\n    \n    if(key[0] === \"?\") {\n      piClosingChar = \"?\";\n      tagEndExp = \"\";\n    }\n  \n    // attrStr is an empty string in case the attribute came as undefined or null\n    if ((attrStr || attrStr === '') && val.indexOf('<') === -1) {\n      return ( this.indentate(level) + '<' +  key + attrStr + piClosingChar + '>' + val + tagEndExp );\n    } else if (this.options.commentPropName !== false && key === this.options.commentPropName && piClosingChar.length === 0) {\n      return this.indentate(level) + `<!--${val}-->` + this.newLine;\n    }else {\n      return (\n        this.indentate(level) + '<' + key + attrStr + piClosingChar + this.tagEndChar +\n        val +\n        this.indentate(level) + tagEndExp    );\n    }\n  }\n}\n\nBuilder.prototype.closeTag = function(key){\n  let closeTag = \"\";\n  if(this.options.unpairedTags.indexOf(key) !== -1){ //unpaired\n    if(!this.options.suppressUnpairedNode) closeTag = \"/\"\n  }else if(this.options.suppressEmptyNode){ //empty\n    closeTag = \"/\";\n  }else{\n    closeTag = `></${key}`\n  }\n  return closeTag;\n}\n\nfunction buildEmptyObjNode(val, key, attrStr, level) {\n  if (val !== '') {\n    return this.buildObjectNode(val, key, attrStr, level);\n  } else {\n    if(key[0] === \"?\") return  this.indentate(level) + '<' + key + attrStr+ '?' + this.tagEndChar;\n    else {\n      return  this.indentate(level) + '<' + key + attrStr + '/' + this.tagEndChar;\n      // return this.buildTagStr(level,key, attrStr);\n    }\n  }\n}\n\nBuilder.prototype.buildTextValNode = function(val, key, attrStr, level) {\n  if (this.options.cdataPropName !== false && key === this.options.cdataPropName) {\n    return this.indentate(level) + `<![CDATA[${val}]]>` +  this.newLine;\n  }else if (this.options.commentPropName !== false && key === this.options.commentPropName) {\n    return this.indentate(level) + `<!--${val}-->` +  this.newLine;\n  }else if(key[0] === \"?\") {//PI tag\n    return  this.indentate(level) + '<' + key + attrStr+ '?' + this.tagEndChar; \n  }else{\n    let textValue = this.options.tagValueProcessor(key, val);\n    textValue = this.replaceEntitiesValue(textValue);\n  \n    if( textValue === ''){\n      return this.indentate(level) + '<' + key + attrStr + this.closeTag(key) + this.tagEndChar;\n    }else{\n      return this.indentate(level) + '<' + key + attrStr + '>' +\n         textValue +\n        '</' + key + this.tagEndChar;\n    }\n  }\n}\n\nBuilder.prototype.replaceEntitiesValue = function(textValue){\n  if(textValue && textValue.length > 0 && this.options.processEntities){\n    for (let i=0; i<this.options.entities.length; i++) {\n      const entity = this.options.entities[i];\n      textValue = textValue.replace(entity.regex, entity.val);\n    }\n  }\n  return textValue;\n}\n\nfunction indentate(level) {\n  return this.options.indentBy.repeat(level);\n}\n\nfunction isAttribute(name /*, options*/) {\n  if (name.startsWith(this.options.attributeNamePrefix) && name !== this.options.textNodeName) {\n    return name.substr(this.attrPrefixLen);\n  } else {\n    return false;\n  }\n}\n\n", "'use strict';\n\nimport {validate} from './validator.js';\nimport XMLParser from './xmlparser/XMLParser.js';\nimport XMLBuilder from './xmlbuilder/json2xml.js';\n\nconst XMLValidator = {\n  validate: validate\n}\nexport {\n  XMLParser,\n  XMLValidator,\n  XMLBuilder\n};"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "__webpack_require__", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "nameStartChar", "regexName", "RegExp", "getAllMatches", "string", "regex", "matches", "match", "exec", "allmatches", "startIndex", "lastIndex", "length", "len", "index", "push", "isName", "defaultOptions", "allowBooleanAttributes", "unpairedTags", "validate", "xmlData", "options", "assign", "tags", "tagFound", "reachedRoot", "substr", "i", "readPI", "err", "isWhiteSpace", "getErrorObject", "getLineNumberForPosition", "tagStartPos", "readCommentAndCDATA", "closingTag", "tagName", "trim", "substring", "result", "readAttributeStr", "attrStr", "attrStrStart", "<PERSON><PERSON><PERSON><PERSON>", "validateAttributeString", "code", "msg", "line", "tagClosed", "otg", "pop", "openPos", "col", "indexOf", "afterAmp", "validateAmpersand", "JSON", "stringify", "map", "t", "replace", "char", "start", "tagname", "angleBracketsCount", "doubleQuote", "singleQuote", "startChar", "validAttrStrRegxp", "attrNames", "getPositionFromMatch", "undefined", "attrName", "validateAttrName", "re", "validateNumberAmpersand", "count", "message", "lineNumber", "lines", "split", "METADATA_SYMBOL", "preserveOrder", "attributeNamePrefix", "attributesGroupName", "textNodeName", "ignoreAttributes", "removeNSPrefix", "parseTagValue", "parseAttributeValue", "trimValues", "cdataPropName", "numberParseOptions", "hex", "leadingZeros", "eNotation", "tagValueProcessor", "val", "attributeValueProcessor", "stopNodes", "alwaysCreateTextNode", "isArray", "commentPropName", "processEntities", "htmlEntities", "ignoreDeclaration", "ignorePiTags", "transformTagName", "transformAttributeName", "updateTag", "jPath", "attrs", "captureMetaData", "XmlNode", "child", "_proto", "add", "_this$child$push", "<PERSON><PERSON><PERSON><PERSON>", "node", "_this$child$push2", "_this$child$push3", "keys", "getMetaDataSymbol", "readDocType", "entities", "Error", "hasBody", "comment", "hasSeq", "entityName", "_readEntityExp", "readEntityExp", "regx", "readElementExp", "readNotationExp", "skipWhitespace", "data", "test", "validateEntityName", "toUpperCase", "_readIdentifierVal", "readIdentifierVal", "notationName", "identifierType", "publicIdentifier", "systemIdentifier", "_readIdentifierVal2", "_readIdentifierVal3", "_readIdentifierVal4", "type", "identifierVal", "elementName", "contentModel", "seq", "j", "name", "hexRegex", "numRegex", "consider", "decimalPoint", "eNotationRegx", "getIgnoreAttributesFn", "Array", "_step", "_iterator", "_createForOfIteratorHelperLoose", "done", "pattern", "Ordered<PERSON>bj<PERSON><PERSON><PERSON>", "currentNode", "tagsNodeStack", "docTypeEntities", "lastEntities", "ampEntity", "_", "str", "String", "fromCodePoint", "Number", "parseInt", "addExternalEntities", "parseXml", "parseTextData", "resolveNameSpace", "buildAttributesMap", "isItStopNode", "replaceEntitiesValue", "readStopNodeData", "saveTextToParentTag", "ignoreAttributesFn", "externalEntities", "ent<PERSON><PERSON>s", "ent", "dontTrim", "hasAttributes", "isLeafNode", "escapeEntities", "newval", "parseValue", "prefix", "char<PERSON>t", "attrsRegx", "oldVal", "aName", "newVal", "attrCollection", "xmlObj", "xmlNode", "textData", "closeIndex", "findClosingIndex", "colonIndex", "lastTagName", "lastIndexOf", "propIndex", "tagData", "readTagExp", "childNode", "tagExp", "attrExpPresent", "endIndex", "_ref", "_ref2", "rawTagName", "lastTag", "tagContent", "entity", "currentTagName", "allNodesExp", "stopNodePath", "stopNodeExp", "errMsg", "closingIndex", "closingChar", "attrBoundary", "ch", "tagExpWithClosingIndex", "separatorIndex", "search", "trimStart", "openTagCount", "<PERSON><PERSON><PERSON><PERSON>", "trimmedStr", "skipLike", "numStr", "window", "parse_int", "notation", "sign", "eChar", "eAdjacentToLeadingZeros", "startsWith", "resolveEnotation", "numTrimmedByZeros", "decimalAdjacentToLeadingZeros", "num", "parsedStr", "n", "toNumber", "prettify", "compress", "arr", "text", "compressedObj", "newJpath", "tagObj", "property", "propName", "<PERSON><PERSON><PERSON><PERSON>", "isLeafTag", "assignAttributes", "attrMap", "jpath", "atrrName", "propCount", "XMLParser", "buildOptions", "parse", "validationOption", "toString", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "orderedResult", "addEntity", "toXml", "jArray", "indentation", "format", "indentBy", "arrToStr", "xmlStr", "isPreviousElementTag", "new<PERSON><PERSON>", "newIdentation", "tagStart", "attr_to_str", "tagValue", "suppressUnpairedNode", "suppressEmptyNode", "endsWith", "includes", "attStr", "tempInd", "piTextNodeName", "tagText", "isStopNode", "attr", "attrVal", "suppressBooleanAttributes", "textValue", "a", "oneListGroup", "Builder", "isAttribute", "attrPrefixLen", "processTextOrObjNode", "indentate", "tagEndChar", "newLine", "object", "level", "<PERSON><PERSON><PERSON><PERSON>", "j2x", "concat", "buildTextValNode", "buildObjectNode", "repeat", "build", "jObj", "buildFromOrderedJs", "arrayNodeName", "_jObj", "join", "Date", "buildAttrPairStr", "arr<PERSON>en", "listTagVal", "listTagAttr", "item", "Ks", "L", "closeTag", "tagEndExp", "piClosingChar", "XMLValidator"], "sourceRoot": ""}