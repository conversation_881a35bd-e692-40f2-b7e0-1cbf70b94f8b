"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { LuMail, LuLock, LuEye, LuEyeOff, LuUser, LuPhone, LuGlobe } from "react-icons/lu";
import { FcGoogle } from "react-icons/fc";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { auth } from "@/lib/firebase";
import {
  signInWithEmailAndPassword,
  GoogleAuthProvider,
  signInWithPopup,
  sendPasswordResetEmail
} from "firebase/auth";
import { useRouter } from "next/navigation";
import {
  Loader2,
  User<PERSON>con,
  BotIcon,
  BuildingIcon
} from "lucide-react";
import { useAuth } from "@/hooks/use-auth";

const googleProvider = new GoogleAuthProvider();

export default function AuthModal({
  open,
  onClose,
  defaultRole
}: {
  open: boolean;
  onClose: () => void;
  defaultRole?: "user" | "brand" | "agent";
}) {

  const router = useRouter();

  const { role: authRole } = useAuth();
  console.log("AuthModal: authRole", authRole);
  const [selectedRole, setSelectedRole] = useState<"user" | "brand" | "agent" | "">("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [website, setWebsite] = useState("");
  const [emailUsername, setEmailUsername] = useState("");
  const [websiteDomain, setWebsiteDomain] = useState("");
  const [phone, setPhone] = useState("");
  const [error, setError] = useState("");
  const [emailError, setEmailError] = useState<string | null>(null);
  const [submitted, setSubmitted] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (defaultRole && selectedRole === "") {
      setSelectedRole(defaultRole);
    } else if (!defaultRole && selectedRole === "") {
      setSelectedRole("user"); // fallback default
    }
  }, [defaultRole, selectedRole]);

  // Helper function to extract domain from website URL
  const extractDomainFromWebsite = (websiteUrl: string): string => {
    try {
      let url = websiteUrl.trim();
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url;
      }
      const domain = new URL(url).hostname.toLowerCase();
      return domain.startsWith('www.') ? domain.substring(4) : domain;
    } catch {
      return '';
    }
  };

  // Handle website URL change and extract domain
  const handleWebsiteChange = (value: string) => {
    setWebsite(value);
    const domain = extractDomainFromWebsite(value);
    setWebsiteDomain(domain);

    // Update email if we have a username
    if (emailUsername && domain) {
      setEmail(`${emailUsername}@${domain}`);
    }
  };

  // Handle email username change
  const handleEmailUsernameChange = (value: string) => {
    setEmailUsername(value);

    // Update full email if we have a domain
    if (value && websiteDomain) {
      setEmail(`${value}@${websiteDomain}`);
    }
  };


  const { user } = useAuth();

  const handleSuccess = async () => {
    onClose();

    if (user) {
      try {
        // Force refresh to get updated claims
        await user.getIdTokenResult(true);

        // Just redirect to /dashboard which will handle routing based on role
        router.push("/dashboard");
      } catch (error) {
        console.error("Failed to fetch custom claims after login:", error);
        // Still redirect to /dashboard which will handle routing once role is loaded
        router.push("/dashboard");
      }
    } else {
      // fallback in case user is null (shouldn't happen here)
      router.push("/dashboard");
    }
  };


  const handleEmailSignIn = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedRole) {
      toast.error("Please select your role to continue");
      return;
    }

    setError("");
    setIsLoading(true);
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const idToken = await userCredential.user.getIdToken();

      // Update the user's role in Firebase
      await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/update-role`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${idToken}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          role: selectedRole
        })
      });

      handleSuccess();
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmailSignUp = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedRole) {
      toast.error("Please select your role to continue");
      return;
    }

    // Additional validation for brands
    if (selectedRole === "brand") {
      if (!website) {
        toast.error("Please enter your company website");
        return;
      }
      if (!companyName) {
        toast.error("Please enter your company name");
        return;
      }
      if (!emailUsername) {
        toast.error("Please enter your email username");
        return;
      }
    }

    setError("");
    setIsLoading(true);
    try {
      const requestBody: {
        email: string;
        password: string;
        role: string;
        name?: string;
        company_name?: string;
        website?: string;
      } = {
        email,
        password,
        role: selectedRole
      };

      // Add role-specific fields
      if (selectedRole === "brand") {
        requestBody.company_name = companyName;
        requestBody.website = website;
      } else {
        requestBody.name = name;
      }

      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/email-register`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody)
      });

      const json = await res.json();
      if (!res.ok) throw new Error(json.detail || "Registration failed");

      await signInWithEmailAndPassword(auth, email, password);
      handleSuccess();
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    if (!selectedRole) {
      toast.error("Please select your role to continue");
      return;
    }

    // Disable Google sign-in for brands
    if (selectedRole === "brand") {
      toast.error("Brand registration requires email/password signup with company verification");
      return;
    }

    setError("");
    setIsLoading(true);

    try {
      const result = await signInWithPopup(auth, googleProvider);
      const idToken = await result.user.getIdToken();

      await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/google-onboard`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${idToken}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          role: selectedRole === "agent" ? "agent" : selectedRole.toLowerCase()
        })
      });

      handleSuccess();
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setIsLoading(false);
    }
  };


  const handleWaitlistSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedRole) {
      toast.error("Please select your role to continue");
      return;
    }

    setSubmitted(true);
    setEmailError(null);

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setEmailError("Please enter a valid email.");
      setSubmitted(false);
      return;
    }
    try {
      // Prepare request body based on role
      const requestBody: {
        email: string;
        role: string;
        website?: string;
        phone?: string;
      } = {
        email,
        role: selectedRole
      };

      // Add website and phone for brands
      if (selectedRole === "brand") {
        if (website) requestBody.website = website;
        if (phone) requestBody.phone = phone;
      }

      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/waitlist/submit`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody)
      });

      const data: { message: string } = await res.json();

      if (res.status === 409) {
        toast.error(data.message || "You're already on the waitlist.");
        return;
      }

      if (!res.ok) {
        throw new Error(data.message || "Something went wrong.");
      }

      toast.success(data.message || "You're on the waitlist!");
      setEmail("");
      setWebsite("");
      setPhone("");
      setSelectedRole("");
      onClose();
    } catch (err) {
      toast.error((err as Error).message || "Unexpected error occurred.");
    } finally {
      setSubmitted(false);

    }

  };

  const handleForgotPassword = async () => {
    if (!email) {
      toast.error("Please enter your email to reset the password.");
      return;
    }

    try {
      await sendPasswordResetEmail(auth, email);
      toast.success("Password reset email sent! Check your inbox.");
    } catch (err) {
      toast.error((err as Error).message);
    }
  };

  // Get appropriate icon for role dropdown
  const getRoleIcon = (roleType: string) => {
    switch (roleType) {
      case "User":
        return <UserIcon className="w-4 h-4" />;
      case "AI Agent":
        return <BotIcon className="w-4 h-4" />;
      case "Brand":
        return <BuildingIcon className="w-4 h-4" />;
      default:
        return null;
    }
  };

  const renderRoleDropdown = () => (
    <div className="space-y-2">
      <label className="text-sm font-medium text-foreground">Select your role</label>
      <Select value={selectedRole} onValueChange={(value: "user" | "brand" | "agent") => setSelectedRole(value)}>
        <SelectTrigger className="h-12 rounded-lg border-input">
          <SelectValue placeholder="Select role" />
        </SelectTrigger>
        <SelectContent>
          {[
            { id: "user", label: "User" },
            { id: "brand", label: "Company/Brand" },
            { id: "agent", label: "AI Agent" }
          ].map((roleOption) => (
            <SelectItem key={roleOption.id} value={roleOption.id} className="flex items-center gap-2">
              <div className="flex items-center gap-2">
                {getRoleIcon(roleOption.id)}
                <span>{roleOption.label}</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );

  // Content for users who aren't eligible for full access
  const renderWaitlistForm = () => {
    // Custom messages based on role
    const getWaitlistContent = () => {
      if (selectedRole === "agent") {
        return {
          icon: <BotIcon className="w-8 h-8" />,
          title: "AI Agent Waitlist",
          description: "If you are an AI agent and would like to join our platform, please join our waitlist."
        };
      } else if (selectedRole === "brand") {
        return {
          icon: <BuildingIcon className="w-8 h-8" />,
          title: "Brand Waitlist",
          description: "If you would like to promote your company or brand (ChatGPT, Perplexity, Claude, etc.), please join our waitlist."
        };
      }
      return {
        icon: <UserIcon className="w-8 h-8" />,
        title: "Join our waitlist",
        description: "We're currently opening access to selected users. Join our waitlist to get early access."
      };
    };

    const content = getWaitlistContent();

    return (
      <div className="space-y-6">
        <div className="text-center space-y-3">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mb-2">
            {content.icon}
          </div>
          <h2 className="text-2xl font-bold text-foreground">{content.title}</h2>
          <p className="text-muted-foreground text-sm px-4">
            {content.description}
          </p>
        </div>

        {renderRoleDropdown()}

        <form onSubmit={handleWaitlistSubmit} className="space-y-4">
          <div className="relative">
            <Input
              type="email"
              placeholder="Your email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="pl-10 h-12 rounded-lg border-input"
              required
            />
            <LuMail className="absolute left-3 top-3.5 text-muted-foreground" />
          </div>

          {selectedRole === "brand" && (
            <>
              <div className="space-y-1">
                <div className="relative">
                  <Input
                    type="url"
                    placeholder="Your website (recommended)"
                    value={website}
                    onChange={(e) => setWebsite(e.target.value)}
                    className="pl-10 h-12 rounded-lg border-input"
                  />
                  <LuGlobe className="absolute left-3 top-3.5 text-muted-foreground" />
                </div>
                <p className="text-xs text-muted-foreground ml-1">Include full URL (e.g., https://example.com)</p>
              </div>

              <div className="space-y-1">
                <div className="relative">
                  <Input
                    type="tel"
                    placeholder="Phone number (optional, for faster outreach)"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    className="pl-10 h-12 rounded-lg border-input"
                  />
                  <LuPhone className="absolute left-3 top-3.5 text-muted-foreground" />
                </div>
                <p className="text-xs text-muted-foreground ml-1">Please include country code (e.g., +1 for US)</p>
              </div>
            </>
          )}

          {emailError && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 rounded-lg p-3 text-sm">
              {emailError}
            </div>
          )}

          <Button
            type="submit"
            className="w-full h-12 rounded-lg bg-primary hover:bg-primary/90 text-primary-foreground"
            disabled={submitted}
          >
            {submitted ? (
              <span className="flex items-center justify-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                Submitting...
              </span>
            ) : (
              "Join Waitlist"
            )}
          </Button>
        </form>
      </div>
    );
  };

  // Brand registration form with website-first flow
  const renderBrandRegistrationForm = () => {
    return (
      <div className="space-y-6">
        <div className="text-center space-y-3">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mb-2">
            <BuildingIcon className="w-8 h-8" />
          </div>
          <h2 className="text-2xl font-bold text-foreground">Register Your Brand</h2>
          <p className="text-muted-foreground text-sm px-4">
            Get started with AdMesh by registering your company. We&apos;ll verify your business email domain.
          </p>
        </div>

        <form onSubmit={handleEmailSignUp} className="space-y-4">
          {/* Website URL - First Field */}
          <div className="space-y-1">
            <label className="text-sm font-medium text-foreground">Company Website</label>
            <div className="relative">
              <Input
                type="url"
                placeholder="https://yourcompany.com"
                value={website}
                onChange={(e) => handleWebsiteChange(e.target.value)}
                className="pl-10 h-12 rounded-lg border-input"
                required
              />
              <LuGlobe className="absolute left-3 top-3.5 text-muted-foreground" />
            </div>
            <p className="text-xs text-muted-foreground ml-1">Enter your company&apos;s main website URL</p>
          </div>

          {/* Email Username with Domain Suffix */}
          {websiteDomain && (
            <div className="space-y-1">
              <label className="text-sm font-medium text-foreground">Work Email</label>
              <div className="flex items-center space-x-2">
                <div className="relative flex-1">
                  <Input
                    type="text"
                    placeholder="username"
                    value={emailUsername}
                    onChange={(e) => handleEmailUsernameChange(e.target.value)}
                    className="pl-10 h-12 rounded-lg border-input"
                    required
                  />
                  <LuMail className="absolute left-3 top-3.5 text-muted-foreground" />
                </div>
                <span className="text-muted-foreground">@{websiteDomain}</span>
              </div>
              <p className="text-xs text-muted-foreground ml-1">Use your work email with the same domain as your website</p>
            </div>
          )}

          {/* Company Name */}
          <div className="space-y-1">
            <label className="text-sm font-medium text-foreground">Company Name</label>
            <div className="relative">
              <Input
                placeholder="Your Company Name"
                value={companyName}
                onChange={(e) => setCompanyName(e.target.value)}
                className="pl-10 h-12 rounded-lg border-input"
                required
              />
              <BuildingIcon className="absolute left-3 top-3.5 text-muted-foreground w-4 h-4" />
            </div>
          </div>

          {/* Password */}
          <div className="space-y-1">
            <label className="text-sm font-medium text-foreground">Password</label>
            <div className="relative">
              <Input
                type={showPassword ? "text" : "password"}
                placeholder="Create a secure password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="pl-10 h-12 rounded-lg border-input pr-10"
                required
              />
              <LuLock className="absolute left-3 top-3.5 text-muted-foreground" />
              <button
                type="button"
                className="absolute right-3 top-3.5 text-muted-foreground hover:text-foreground"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <LuEyeOff /> : <LuEye />}
              </button>
            </div>
          </div>

          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 rounded-lg p-3 text-sm">
              {error}
            </div>
          )}

          <Button
            type="submit"
            disabled={isLoading || !websiteDomain || !emailUsername}
            className="w-full h-12 rounded-lg bg-primary hover:bg-primary/90 text-primary-foreground"
          >
            {isLoading ? (
              <span className="flex items-center justify-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                Creating account...
              </span>
            ) : (
              "Create Brand Account"
            )}
          </Button>

          <p className="text-xs text-muted-foreground text-center">
            By creating an account, you agree to our Terms of Service and Privacy Policy
          </p>
        </form>
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md w-full p-0 overflow-hidden rounded-xl bg-background border shadow-xl">
        <DialogTitle className="sr-only">Authentication</DialogTitle>

        <div className="p-6 sm:p-8">
          {selectedRole === "agent" ? (
            renderWaitlistForm()
          ) : selectedRole === "brand" ? (
            renderBrandRegistrationForm()
          ) : (
            <Tabs defaultValue="signup" className="w-full">
              <TabsList className="grid grid-cols-2 mb-6 bg-muted p-1 rounded-lg">
                <TabsTrigger value="signup" className="rounded-md transition-all">Sign Up</TabsTrigger>
                <TabsTrigger value="signin" className="rounded-md transition-all">Sign In</TabsTrigger>
              </TabsList>

              <TabsContent value="signin">
                <div className="space-y-6">
                  <div className="text-center space-y-2">
                    <h2 className="text-2xl font-bold text-foreground">Welcome back</h2>
                    <p className="text-muted-foreground text-sm">Sign in to your account</p>
                  </div>

                  {renderRoleDropdown()}

                  <form onSubmit={handleEmailSignIn} className="space-y-4">
                    {/* Hide Google sign-in for brands */}
                    {(selectedRole as string) !== "brand" && selectedRole !== "" && (
                      <>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleGoogleSignIn}
                          className="w-full h-12 justify-center gap-2 rounded-lg border-border hover:bg-muted"
                        >
                          <FcGoogle className="w-5 h-5" />
                          Continue with Google
                        </Button>

                        <div className="relative flex items-center justify-center text-xs text-muted-foreground my-6">
                          <div className="border-t border-border w-full absolute"></div>
                          <div className="bg-background px-3 z-10">or continue with email</div>
                        </div>
                      </>
                    )}

                    <div className="space-y-4">
                      <div className="relative">
                        <Input
                          type="email"
                          placeholder="Email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className="pl-10 h-12 rounded-lg border-input"
                          required
                        />
                        <LuMail className="absolute left-3 top-3.5 text-muted-foreground" />
                      </div>

                      <div className="relative">
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder="Password"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          className="pl-10 h-12 rounded-lg border-input pr-10"
                          required
                        />
                        <LuLock className="absolute left-3 top-3.5 text-muted-foreground" />
                        <button
                          type="button"
                          className="absolute right-3 top-3.5 text-muted-foreground hover:text-foreground"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? <LuEyeOff /> : <LuEye />}
                        </button>
                      </div>
                    </div>

                    <div className="text-right">
                      <button
                        type="button"
                        className="text-sm text-primary hover:text-primary/80"
                        onClick={handleForgotPassword}
                      >
                        Forgot password?
                      </button>
                    </div>

                    {error && (
                      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 rounded-lg p-3 text-sm">
                        {error}
                      </div>
                    )}

                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="w-full h-12 rounded-lg bg-primary hover:bg-primary/90 text-primary-foreground"
                    >
                      {isLoading ? (
                        <span className="flex items-center justify-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Signing in...
                        </span>
                      ) : (
                        "Sign In"
                      )}
                    </Button>
                  </form>
                </div>
              </TabsContent>

              <TabsContent value="signup">
                <div className="space-y-6">
                  <div className="text-center space-y-2">
                    <h2 className="text-2xl font-bold text-foreground">Create account</h2>
                    <p className="text-muted-foreground text-sm">Sign up to get started</p>
                  </div>

                  {renderRoleDropdown()}

                  <form onSubmit={handleEmailSignUp} className="space-y-4">
                    {/* Hide Google sign-in for brands */}
                    {(selectedRole as string) !== "brand" && selectedRole !== "" && (
                      <>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleGoogleSignIn}
                          className="w-full h-12 justify-center gap-2 rounded-lg border-border hover:bg-muted"
                        >
                          <FcGoogle className="w-5 h-5" />
                          Continue with Google
                        </Button>

                        <div className="relative flex items-center justify-center text-xs text-muted-foreground my-6">
                          <div className="border-t border-border w-full absolute"></div>
                          <div className="bg-background px-3 z-10">or create an account</div>
                        </div>
                      </>
                    )}

                    <div className="space-y-4">
                      <div className="relative">
                        <Input
                          type="email"
                          placeholder="Email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className="pl-10 h-12 rounded-lg border-input"
                          required
                        />
                        <LuMail className="absolute left-3 top-3.5 text-muted-foreground" />
                      </div>

                      <div className="relative">
                        <Input
                          placeholder="Full Name"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          className="pl-10 h-12 rounded-lg border-input"
                          required
                        />
                        <LuUser className="absolute left-3 top-3.5 text-muted-foreground" />
                      </div>

                      <div className="relative">
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder="Password"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          className="pl-10 h-12 rounded-lg border-input pr-10"
                          required
                        />
                        <LuLock className="absolute left-3 top-3.5 text-muted-foreground" />
                        <button
                          type="button"
                          className="absolute right-3 top-3.5 text-muted-foreground hover:text-foreground"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? <LuEyeOff /> : <LuEye />}
                        </button>
                      </div>
                    </div>

                    {error && (
                      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 rounded-lg p-3 text-sm">
                        {error}
                      </div>
                    )}

                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="w-full h-12 rounded-lg bg-primary hover:bg-primary/90 text-primary-foreground"
                    >
                      {isLoading ? (
                        <span className="flex items-center justify-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Creating account...
                        </span>
                      ) : (
                        "Create Account"
                      )}
                    </Button>
                  </form>
                </div>
              </TabsContent>
            </Tabs>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}